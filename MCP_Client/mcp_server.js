const express = require('express');
const app = express();
app.use(express.json());

// Example tool: get current time
const tools = [
  {
    name: 'getTime',
    description: 'Returns the current server time',
    parameters: {}
  }
];

app.get('/tools', (req, res) => {
  res.json(tools);
});

app.post('/tool', (req, res) => {
  const { tool, params } = req.body;
  if (tool === 'getTime') {
    res.json({ time: new Date().toISOString() });
  } else {
    res.status(400).json({ error: 'Unknown tool' });
  }
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`MCP Server running on port ${PORT}`);
});
