import json
import requests

# Load config and MCP servers
with open('MCP_servers.json') as f:
    mcp_servers = json.load(f)
with open('config.json') as f:
    config = json.load(f)

AI_API_KEY = config['ai_api_key']
AI_API_URL = config['ai_api_url']

def ask_ai(message):
    headers = {'Authorization': f'Bearer {AI_API_KEY}', 'Content-Type': 'application/json'}
    data = {
        "messages": [{"role": "user", "content": message}],
        "model": "epicai-chat"
    }
    response = requests.post(AI_API_URL, headers=headers, json=data)
    response.raise_for_status()
    return response.json()['choices'][0]['message']['content']

def find_tool_on_server(server_url, user_intent):
    try:
        tools = requests.get(f"{server_url}/tools").json()
        # Naive match: tool name or description contains intent keyword
        for tool in tools:
            if user_intent.lower() in tool['name'].lower() or user_intent.lower() in tool.get('description', '').lower():
                return tool
    except Exception as e:
        print(f"Error querying {server_url}/tools: {e}")
    return None

def use_tool(server_url, tool, user_message):
    # AI helps to build the perfect JSON for the tool
    prompt = f"Given the tool schema: {json.dumps(tool)}, and the user request: '{user_message}', provide the perfect JSON payload to use this tool." 
    payload = ask_ai(prompt)
    try:
        payload_json = json.loads(payload)
    except Exception:
        print("AI did not return valid JSON, using as string.")
        payload_json = payload
    response = requests.post(f"{server_url}/tools/{tool['name']}", json=payload_json)
    response.raise_for_status()
    return response.json()

def main():
    user_message = input("You: ")
    # Step 1: Use AI to analyze message and extract intent
    intent = ask_ai(f"What is the main intent of this user message: '{user_message}'? Respond with a single keyword.")
    # Step 2: Find a tool on any MCP server
    for server in mcp_servers:
        tool = find_tool_on_server(server['url'], intent)
        if tool:
            print(f"Using tool '{tool['name']}' from {server['name']}")
            result = use_tool(server['url'], tool, user_message)
            print(f"MCP_Client: {result}")
            return
    print("No suitable tool found on any MCP server.")

if __name__ == "__main__":
    main()
