const fs = require('fs');
const readline = require('readline');
const axios = require('axios');

const servers = JSON.parse(fs.readFileSync('MCP_servers.json'));
const config = {
  ai_api_key: 'ecs-00',
  ai_api_url: 'https://api.epicai.fun/v1/chat'
};

async function askAI(message) {
  const headers = {
    'Authorization': `Bearer ${config.ai_api_key}`,
    'Content-Type': 'application/json'
  };
  const data = {
    messages: [{ role: 'user', content: message }],
    model: 'epicai-chat'
  };
  const res = await axios.post(config.ai_api_url, data, { headers });
  return res.data.choices[0].message.content;
}

async function findTool(serverUrl, userIntent) {
  try {
    const res = await axios.get(`${serverUrl}/tools`);
    for (const tool of res.data) {
      if (
        tool.name.toLowerCase().includes(userIntent.toLowerCase()) ||
        (tool.description && tool.description.toLowerCase().includes(userIntent.toLowerCase()))
      ) {
        return tool;
      }
    }
  } catch (e) {
    console.error(`Error querying ${serverUrl}/tools:`, e.message);
  }
  return null;
}

async function useTool(serverUrl, tool, userMessage) {
  const prompt = `Given the tool schema: ${JSON.stringify(tool)}, and the user request: '${userMessage}', provide the perfect JSON payload to use this tool.`;
  let payload = await askAI(prompt);
  try {
    payload = JSON.parse(payload);
  } catch {
    // fallback: send as string
  }
  const res = await axios.post(`${serverUrl}/tools/${tool.name}`, payload);
  return res.data;
}

async function main() {
  const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
  rl.question('You: ', async (userMessage) => {
    const intent = await askAI(`What is the main intent of this user message: '${userMessage}'? Respond with a single keyword.`);
    for (const server of servers) {
      const tool = await findTool(server.url, intent);
      if (tool) {
        console.log(`Using tool '${tool.name}' from ${server.name}`);
        const result = await useTool(server.url, tool, userMessage);
        console.log('MCP_Client:', result);
        rl.close();
        return;
      }
    }
    console.log('No suitable tool found on any MCP server.');
    rl.close();
  });
}

if (require.main === module) main();
