const express = require('express');
const app = express();

const allResults = [
  {
    "name": "Assembly2.iam",
    "id": "43",
    "state": "Work in Progress",
    "stateColor": 0,
    "revision": "",
    "category": "Base",
    "categoryColor": -2302756,
    "lastModifiedDate": "2025-06-22T19:17:40.043Z",
    "isCheckedOut": true,
    "hasVisualizationAttachment": true,
    "size": 237568,
    "isCloaked": false,
    "checkinDate": "0001-01-01T00:00:00Z",
    "checkoutDate": "2025-06-22T19:23:18.383Z",
    "checkoutUserName": "TestAccount",
    "isHidden": false,
    "isReadOnly": false,
    "parentFolderId": "2",
    "file": {
      "id": "37",
      "entityType": "File",
      "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/files/37"
    },
    "isOnSite": true,
    "createDate": "2025-06-22T19:17:49.8Z",
    "createUserName": "TestAccount",
    "classification": "None",
    "visualizationAttachmentStatus": "NotSyncronized",
    "version": 3,
    "entityType": "FileVersion",
    "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/file-versions/43"
  },
  {
    "name": "Test1.ipt",
    "id": "40",
    "state": "Released",
    "stateColor": 0,
    "revision": "",
    "category": "Base",
    "categoryColor": -2302756,
    "lastModifiedDate": "2025-06-23T18:46:33.497Z",
    "isCheckedOut": false,
    "hasVisualizationAttachment": true,
    "size": 209920,
    "isCloaked": false,
    "checkinDate": "2025-06-23T19:17:46.297Z",
    "checkoutDate": "2025-06-23T18:45:51.907Z",
    "isHidden": false,
    "isReadOnly": false,
    "parentFolderId": "2",
    "file": {
      "id": "31",
      "entityType": "File",
      "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/files/31"
    },
    "isOnSite": true,
    "createDate": "2025-06-23T19:17:46.297Z",
    "createUserName": "TestAccount",
    "classification": "None",
    "visualizationAttachmentStatus": "NotSyncronized",
    "version": 2,
    "entityType": "FileVersion",
    "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/file-versions/40"
  },
  {
    "name": "Test2.ipt",
    "id": "41",
    "state": "Work in Progress",
    "stateColor": 0,
    "revision": "A",
    "category": "Base",
    "categoryColor": -2302756,
    "lastModifiedDate": "2024-10-18T10:00:00.000Z",
    "isCheckedOut": false,
    "hasVisualizationAttachment": false,
    "size": 123456,
    "isCloaked": false,
    "checkinDate": "2024-10-18T10:00:00.000Z",
    "checkoutDate": "2024-10-18T09:00:00.000Z",
    "isHidden": false,
    "isReadOnly": false,
    "parentFolderId": "2",
    "file": {
      "id": "32",
      "entityType": "File",
      "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/files/32"
    },
    "isOnSite": true,
    "createDate": "2024-10-18T09:00:00.000Z",
    "createUserName": "AnotherUser",
    "classification": "None",
    "visualizationAttachmentStatus": "NotSyncronized",
    "version": 1,
    "entityType": "FileVersion",
    "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/file-versions/41"
  },
  {
    "name": "AssemblyTest.iam",
    "id": "44",
    "state": "Released",
    "stateColor": 0,
    "revision": "B",
    "category": "Base",
    "categoryColor": -2302756,
    "lastModifiedDate": "2024-10-19T12:00:00.000Z",
    "isCheckedOut": false,
    "hasVisualizationAttachment": true,
    "size": 345678,
    "isCloaked": false,
    "checkinDate": "2024-10-19T12:00:00.000Z",
    "checkoutDate": "2024-10-19T11:00:00.000Z",
    "isHidden": false,
    "isReadOnly": false,
    "parentFolderId": "2",
    "file": {
      "id": "38",
      "entityType": "File",
      "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/files/38"
    },
    "isOnSite": true,
    "createDate": "2024-10-19T11:00:00.000Z",
    "createUserName": "TestAccount",
    "classification": "None",
    "visualizationAttachmentStatus": "NotSyncronized",
    "version": 4,
    "entityType": "FileVersion",
    "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/file-versions/44"
  },
  {
    "name": "Readme.txt",
    "id": "45",
    "state": "Work in Progress",
    "stateColor": 0,
    "revision": "C",
    "category": "Documentation",
    "categoryColor": -2302756,
    "lastModifiedDate": "2024-10-20T08:00:00.000Z",
    "isCheckedOut": false,
    "hasVisualizationAttachment": false,
    "size": 2048,
    "isCloaked": false,
    "checkinDate": "2024-10-20T08:00:00.000Z",
    "checkoutDate": "2024-10-20T07:00:00.000Z",
    "isHidden": false,
    "isReadOnly": false,
    "parentFolderId": "2",
    "file": {
      "id": "39",
      "entityType": "File",
      "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/files/39"
    },
    "isOnSite": true,
    "createDate": "2024-10-20T07:00:00.000Z",
    "createUserName": "DocUser",
    "classification": "Documentation",
    "visualizationAttachmentStatus": "NotSyncronized",
    "version": 1,
    "entityType": "FileVersion",
    "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/file-versions/45"
  }
];

app.get("/", (req, res) => {
  res.send("Hello World!");
});

app.get('/AutodeskDM/Services/api/vault/v2/vaults/:vaultId/file-versions', (req, res) => {
  const { q } = req.query;
  let results = allResults;

  // --- FILTERS ---
  const filterCheckoutUserName = req.query['filter[CheckoutUserName]'];
  const filterCreateUserName = req.query['filter[CreateUserName]'];
  const filterCategoryName = req.query['filter[CategoryName]'];
  const filterState = req.query['filter[State]'];

  if (q) {
    const qLower = q.toLowerCase();
    results = results.filter(obj => {
      return (
        (obj.name && obj.name.toLowerCase().includes(qLower)) ||
        (obj.id && obj.id.toLowerCase().includes(qLower)) ||
        (obj.category && obj.category.toLowerCase().includes(qLower)) ||
        (obj.createUserName && obj.createUserName.toLowerCase().includes(qLower)) ||
        (obj.file && obj.file.id && obj.file.id.toLowerCase().includes(qLower))
      );
    });
  }
  if (filterCheckoutUserName) {
    results = results.filter(obj => (obj.checkoutUserName || obj.checkoutUserName === '') && String(obj.checkoutUserName).toLowerCase() === String(filterCheckoutUserName).toLowerCase());
  }
  if (filterCreateUserName) {
    results = results.filter(obj => (obj.createUserName || obj.createUserName === '') && String(obj.createUserName).toLowerCase() === String(filterCreateUserName).toLowerCase());
  }
  if (filterCategoryName) {
    results = results.filter(obj => (obj.category || obj.category === '') && String(obj.category).toLowerCase() === String(filterCategoryName).toLowerCase());
  }
  if (filterState) {
    results = results.filter(obj => (obj.state || obj.state === '') && String(obj.state).toLowerCase() === String(filterState).toLowerCase());
  }

  // --- OPTIONS ---
  const optionLatestOnly = req.query['option[latestOnly]'] !== 'false'; // default true
  const optionReleasedFilesOnly = req.query['option[releasedFilesOnly]'] === 'true'; // default false
  const optionExtendedModels = req.query['option[extendedModels]'] === 'true'; // default false
  const optionPropDefIds = req.query['option[propDefIds]'];
  const sort = req.query['sort'];
  const limit = req.query['limit'] ? parseInt(req.query['limit']) : 2;
  // cursorState is not implemented in this mock

  // Only latest version (mock: highest version per name)
  if (optionLatestOnly) {
    const latestMap = {};
    results.forEach(obj => {
      if (!latestMap[obj.name] || obj.version > latestMap[obj.name].version) {
        latestMap[obj.name] = obj;
      }
    });
    results = Object.values(latestMap);
  }

  // Only released files (mock: state === 'Released')
  if (optionReleasedFilesOnly) {
    results = results.filter(obj => String(obj.state).toLowerCase() === 'released');
  }

  // Extended models (mock: add dummy extra field)
  if (optionExtendedModels) {
    results = results.map(obj => ({ ...obj, extendedInfo: 'This is extended model info (mock)' }));
  }

  // Property filtering (mock: if not 'all', only return those keys)
  if (optionPropDefIds && optionPropDefIds !== 'all') {
    const propIds = optionPropDefIds.split(',').map(s => s.trim());
    results = results.map(obj => {
      const filtered = {};
      propIds.forEach(pid => { if (obj[pid] !== undefined) filtered[pid] = obj[pid]; });
      return filtered;
    });
  }

  // Sorting (mock: only supports one or two fields, e.g. 'Revision desc,Name asc')
  if (sort) {
    const sortFields = sort.split(',').map(s => s.trim());
    results.sort((a, b) => {
      for (const field of sortFields) {
        const [prop, order] = field.split(' ');
        if (a[prop] < b[prop]) return order === 'desc' ? 1 : -1;
        if (a[prop] > b[prop]) return order === 'desc' ? -1 : 1;
      }
      return 0;
    });
  }

  // Pagination (limit)
  const pagedResults = results.slice(0, limit);

  res.json({
    "pagination": {
      "limit": limit,
      "totalResults": results.length,
      "nextUrl": null,
      "indexingStatus": "IndexingComplete"
    },
    "results": pagedResults,
    "included": {
      "folder": {
        "2": {
          "name": "ColinTest",
          "id": "2",
          "fullName": "$/ColinTest",
          "category": "Folder",
          "categoryColor": -2302756,
          "stateColor": 0,
          "subfolderCount": 0,
          "isLibrary": false,
          "isCloaked": false,
          "isReadOnly": false,
          "createDate": "2024-10-11T07:04:11.723Z",
          "createUserName": "TestAccount",
          "entityType": "Folder",
          "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/folders/2"
        }
      }
    }
  });
});

const PORT = process.env.PORT || 4000;
app.listen(PORT, () => {
  console.log(`Vault Mock API running on port ${PORT}`);
});
