<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vault MCP Test Interface</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        .status-item {
            padding: 10px 15px;
            border-radius: 5px;
            font-weight: bold;
            min-width: 150px;
            text-align: center;
        }
        .status-ok { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-unknown { background: #e2e3e5; color: #383d41; }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
            color: #155724;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 150px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Vault MCP Test Interface</h1>
        
        <div class="status">
            <div id="mockApiStatus" class="status-item status-unknown">Mock API: Checking...</div>
            <div id="serverStatus" class="status-item status-unknown">MCP Server: Checking...</div>
            <div id="clientStatus" class="status-item status-unknown">MCP Client: Checking...</div>
        </div>

        <!-- Test Tools -->
        <div class="test-section">
            <h3>🔧 Test Available Tools</h3>
            <button onclick="testGetTools()">Get Available Tools</button>
            <div id="toolsResult" class="result" style="display: none;"></div>
        </div>

        <!-- Test getTime Tool -->
        <div class="test-section">
            <h3>⏰ Test getTime Tool</h3>
            <button onclick="testGetTime()">Get Current Time</button>
            <div id="timeResult" class="result" style="display: none;"></div>
        </div>

        <!-- Test getVaultFiles Tool -->
        <div class="test-section">
            <h3>📁 Test getVaultFiles Tool</h3>
            <div class="form-group">
                <label>Vault ID:</label>
                <input type="text" id="vaultId" value="117" placeholder="117">
            </div>
            <div class="form-group">
                <label>Released Files Only:</label>
                <select id="releasedOnly">
                    <option value="false">No</option>
                    <option value="true">Yes</option>
                </select>
            </div>
            <div class="form-group">
                <label>Limit:</label>
                <input type="number" id="limit" value="5" min="1" max="100">
            </div>
            <button onclick="testGetVaultFiles()">Get Vault Files</button>
            <div id="vaultFilesResult" class="result" style="display: none;"></div>
        </div>

        <!-- Test Direct Tool Execution -->
        <div class="test-section">
            <h3>🛠️ Test Direct Tool Execution</h3>
            <div class="form-group">
                <label>Tool Name:</label>
                <select id="toolName">
                    <option value="getTime">getTime</option>
                    <option value="getVaultFiles">getVaultFiles</option>
                </select>
            </div>
            <div class="form-group">
                <label>Parameters (JSON):</label>
                <input type="text" id="toolParams" placeholder='{"vaultId": "117"}' style="width: 300px;">
            </div>
            <button onclick="testDirectTool()">Execute Tool</button>
            <div id="directToolResult" class="result" style="display: none;"></div>
        </div>

        <!-- Test Chat (without AI) -->
        <div class="test-section">
            <h3>💬 Test Chat Interface (Direct Tool Call)</h3>
            <p><em>Note: This bypasses AI and directly calls tools for testing</em></p>
            <button onclick="testDirectVaultFiles()">Test: "Show me vault files"</button>
            <button onclick="testDirectTime()">Test: "What time is it?"</button>
            <div id="chatResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // Check service status on load
        window.onload = function() {
            checkServiceStatus();
        };

        async function checkServiceStatus() {
            // Check Mock API
            try {
                const response = await fetch('http://localhost:4000');
                document.getElementById('mockApiStatus').textContent = 'Mock API: ✅ Running';
                document.getElementById('mockApiStatus').className = 'status-item status-ok';
            } catch (error) {
                document.getElementById('mockApiStatus').textContent = 'Mock API: ❌ Down';
                document.getElementById('mockApiStatus').className = 'status-item status-error';
            }

            // Check MCP Server
            try {
                const response = await fetch('http://localhost:5001/health');
                document.getElementById('serverStatus').textContent = 'MCP Server: ✅ Running';
                document.getElementById('serverStatus').className = 'status-item status-ok';
            } catch (error) {
                document.getElementById('serverStatus').textContent = 'MCP Server: ❌ Down';
                document.getElementById('serverStatus').className = 'status-item status-error';
            }

            // Check MCP Client
            try {
                const response = await fetch('http://localhost:5098/health');
                document.getElementById('clientStatus').textContent = 'MCP Client: ✅ Running';
                document.getElementById('clientStatus').className = 'status-item status-ok';
            } catch (error) {
                document.getElementById('clientStatus').textContent = 'MCP Client: ❌ Down';
                document.getElementById('clientStatus').className = 'status-item status-error';
            }
        }

        async function testGetTools() {
            const resultDiv = document.getElementById('toolsResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch('http://localhost:5001/tools');
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testGetTime() {
            const resultDiv = document.getElementById('timeResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch('http://localhost:5001/tool', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ tool: 'getTime', params: {} })
                });
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testGetVaultFiles() {
            const resultDiv = document.getElementById('vaultFilesResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Loading...';
            
            const vaultId = document.getElementById('vaultId').value;
            const releasedOnly = document.getElementById('releasedOnly').value === 'true';
            const limit = parseInt(document.getElementById('limit').value);
            
            const params = {
                vaultId: vaultId,
                option_releasedFilesOnly: releasedOnly,
                limit: limit
            };
            
            try {
                const response = await fetch('http://localhost:5001/tool', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ tool: 'getVaultFiles', params: params })
                });
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testDirectTool() {
            const resultDiv = document.getElementById('directToolResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Loading...';
            
            const toolName = document.getElementById('toolName').value;
            const paramsText = document.getElementById('toolParams').value;
            
            let params = {};
            if (paramsText.trim()) {
                try {
                    params = JSON.parse(paramsText);
                } catch (e) {
                    resultDiv.textContent = 'Error: Invalid JSON parameters';
                    resultDiv.className = 'result error';
                    return;
                }
            }
            
            try {
                const response = await fetch('http://localhost:5001/tool', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ tool: toolName, params: params })
                });
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testDirectVaultFiles() {
            const resultDiv = document.getElementById('chatResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Testing vault files request...';
            
            try {
                const response = await fetch('http://localhost:5001/tool', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        tool: 'getVaultFiles', 
                        params: { vaultId: '117', limit: 3 } 
                    })
                });
                const data = await response.json();
                
                if (data.success && data.data && data.data.files) {
                    let result = `Found ${data.data.files.length} files:\n\n`;
                    data.data.files.forEach((file, index) => {
                        result += `${index + 1}. ${file.name}\n`;
                        result += `   Path: ${file.path}\n`;
                        result += `   State: ${file.state}\n`;
                        result += `   Modified: ${new Date(file.modifiedDate).toLocaleDateString()}\n\n`;
                    });
                    resultDiv.textContent = result;
                } else {
                    resultDiv.textContent = JSON.stringify(data, null, 2);
                }
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testDirectTime() {
            const resultDiv = document.getElementById('chatResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Getting current time...';
            
            try {
                const response = await fetch('http://localhost:5001/tool', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ tool: 'getTime', params: {} })
                });
                const data = await response.json();
                
                if (data.success && data.data && data.data.time) {
                    const time = new Date(data.data.time);
                    resultDiv.textContent = `Current server time: ${time.toLocaleString()}`;
                } else {
                    resultDiv.textContent = JSON.stringify(data, null, 2);
                }
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
