<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vault MCP Client - AI Assistant</title>
  <link rel="stylesheet" href="styles.css">
  <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤖</text></svg>">
</head>
<body>
  <div class="app-container">
    <header class="app-header">
      <h1>🤖 Vault MCP Assistant</h1>
      <p>AI-powered assistant for Autodesk Vault interactions</p>
      <div class="status-indicator" id="statusIndicator">
        <span class="status-dot" id="statusDot"></span>
        <span id="statusText">Connecting...</span>
      </div>
    </header>

    <main class="chat-container">
      <div class="messages-container" id="messagesContainer">
        <div class="welcome-message">
          <h3>👋 Welcome!</h3>
          <p>I'm your AI assistant for Autodesk Vault. I can help you:</p>
          <ul>
            <li>🔍 Search and retrieve vault files</li>
            <li>📊 Get file information and properties</li>
            <li>⏰ Check current server time</li>
            <li>❓ Answer questions about your vault data</li>
          </ul>
          <p>Try asking something like: <em>"Show me the latest CAD files"</em> or <em>"What time is it?"</em></p>
        </div>
      </div>

      <div class="input-container">
        <form class="input-form" id="inputForm">
          <div class="input-wrapper">
            <textarea 
              id="messageInput" 
              placeholder="Ask me anything about your Vault files..."
              rows="1"
              maxlength="1000"
            ></textarea>
            <button type="submit" id="sendButton" class="send-button" disabled>
              <span class="send-icon">📤</span>
              <span class="send-text">Send</span>
            </button>
          </div>
          <div class="input-footer">
            <span class="char-counter" id="charCounter">0/1000</span>
            <span class="typing-indicator" id="typingIndicator" style="display: none;">
              <span class="dot"></span>
              <span class="dot"></span>
              <span class="dot"></span>
              AI is thinking...
            </span>
          </div>
        </form>
      </div>
    </main>

    <footer class="app-footer">
      <div class="footer-content">
        <span>Vault MCP Client v2.0</span>
        <div class="footer-links">
          <a href="/swagger" target="_blank">API Docs</a>
          <a href="/health" target="_blank">Health Check</a>
          <button id="clearChatButton" class="clear-button">Clear Chat</button>
        </div>
      </div>
    </footer>
  </div>

  <!-- Error Modal -->
  <div class="modal" id="errorModal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3>⚠️ Error</h3>
        <button class="modal-close" id="modalClose">&times;</button>
      </div>
      <div class="modal-body">
        <p id="errorMessage">An error occurred</p>
      </div>
      <div class="modal-footer">
        <button class="button button-primary" id="modalOk">OK</button>
      </div>
    </div>
  </div>

  <script src="app.js"></script>
</body>
</html>
