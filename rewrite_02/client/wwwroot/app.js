// Vault MCP Client - Enhanced Frontend Application
class VaultMCPClient {
  constructor() {
    this.sessionId = this.generateSessionId();
    this.isConnected = false;
    this.isLoading = false;
    this.messageHistory = [];
    
    this.initializeElements();
    this.attachEventListeners();
    this.checkServerStatus();
    this.loadChatHistory();
  }

  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  initializeElements() {
    // Main elements
    this.messagesContainer = document.getElementById('messagesContainer');
    this.messageInput = document.getElementById('messageInput');
    this.sendButton = document.getElementById('sendButton');
    this.inputForm = document.getElementById('inputForm');
    this.charCounter = document.getElementById('charCounter');
    this.typingIndicator = document.getElementById('typingIndicator');
    
    // Status elements
    this.statusIndicator = document.getElementById('statusIndicator');
    this.statusDot = document.getElementById('statusDot');
    this.statusText = document.getElementById('statusText');
    
    // Modal elements
    this.errorModal = document.getElementById('errorModal');
    this.errorMessage = document.getElementById('errorMessage');
    this.modalClose = document.getElementById('modalClose');
    this.modalOk = document.getElementById('modalOk');
    
    // Footer elements
    this.clearChatButton = document.getElementById('clearChatButton');
  }

  attachEventListeners() {
    // Form submission
    this.inputForm.addEventListener('submit', (e) => this.handleSubmit(e));
    
    // Input events
    this.messageInput.addEventListener('input', () => this.handleInputChange());
    this.messageInput.addEventListener('keydown', (e) => this.handleKeyDown(e));
    
    // Modal events
    this.modalClose.addEventListener('click', () => this.hideErrorModal());
    this.modalOk.addEventListener('click', () => this.hideErrorModal());
    
    // Clear chat
    this.clearChatButton.addEventListener('click', () => this.clearChat());
    
    // Auto-resize textarea
    this.messageInput.addEventListener('input', () => this.autoResizeTextarea());
    
    // Window events
    window.addEventListener('beforeunload', () => this.saveChatHistory());
  }

  handleInputChange() {
    const length = this.messageInput.value.length;
    this.charCounter.textContent = `${length}/1000`;
    
    // Enable/disable send button
    const hasText = length > 0 && length <= 1000;
    this.sendButton.disabled = !hasText || this.isLoading;
    
    // Update character counter color
    if (length > 900) {
      this.charCounter.style.color = '#ef4444';
    } else if (length > 800) {
      this.charCounter.style.color = '#f59e0b';
    } else {
      this.charCounter.style.color = '#6b7280';
    }
  }

  handleKeyDown(e) {
    // Send on Ctrl+Enter or Cmd+Enter
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      e.preventDefault();
      if (!this.sendButton.disabled) {
        this.handleSubmit(e);
      }
    }
  }

  autoResizeTextarea() {
    this.messageInput.style.height = 'auto';
    this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
  }

  async handleSubmit(e) {
    e.preventDefault();
    
    const message = this.messageInput.value.trim();
    if (!message || this.isLoading) return;
    
    // Add user message to chat
    this.addMessage(message, 'user');
    
    // Clear input and show loading state
    this.messageInput.value = '';
    this.handleInputChange();
    this.autoResizeTextarea();
    this.setLoadingState(true);
    
    try {
      // Send message to server
      const response = await this.sendMessage(message);
      
      // Add assistant response
      this.addMessage(response.reply, 'assistant', {
        toolsUsed: response.toolsUsed,
        timestamp: response.timestamp,
        success: response.success
      });
      
      if (!response.success && response.error) {
        console.warn('Server returned error:', response.error);
      }
      
    } catch (error) {
      console.error('Error sending message:', error);
      this.addMessage(
        'Sorry, I encountered an error while processing your request. Please try again.',
        'assistant',
        { error: true }
      );
      this.showErrorModal('Connection Error', error.message);
    } finally {
      this.setLoadingState(false);
      this.messageInput.focus();
    }
  }

  async sendMessage(message) {
    const response = await fetch('/ask', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: message,
        sessionId: this.sessionId
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  addMessage(content, sender, metadata = {}) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;
    
    const timestamp = new Date().toLocaleTimeString();
    const senderName = sender === 'user' ? 'You' : 'AI Assistant';
    
    let messageHTML = `
      <div class="message-header">${senderName}</div>
      <div class="message-content">${this.formatMessageContent(content)}</div>
      <div class="message-footer">
        <span class="timestamp">${timestamp}</span>
    `;
    
    // Add tools used if available
    if (metadata.toolsUsed && metadata.toolsUsed.length > 0) {
      const toolBadges = metadata.toolsUsed.map(tool => 
        `<span class="tool-badge">${tool}</span>`
      ).join('');
      messageHTML += `<div class="tools-used">${toolBadges}</div>`;
    }
    
    messageHTML += '</div>';
    messageDiv.innerHTML = messageHTML;
    
    // Add error styling if needed
    if (metadata.error) {
      messageDiv.style.borderLeft = '4px solid #ef4444';
      messageDiv.style.background = '#fef2f2';
    }
    
    this.messagesContainer.appendChild(messageDiv);
    this.scrollToBottom();
    
    // Store in history
    this.messageHistory.push({
      content,
      sender,
      timestamp: new Date().toISOString(),
      metadata
    });
  }

  formatMessageContent(content) {
    // Basic formatting for better readability
    return content
      .replace(/\n/g, '<br>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>');
  }

  setLoadingState(loading) {
    this.isLoading = loading;
    
    if (loading) {
      this.sendButton.classList.add('loading');
      this.sendButton.disabled = true;
      this.sendButton.querySelector('.send-text').textContent = 'Sending...';
      this.typingIndicator.style.display = 'flex';
    } else {
      this.sendButton.classList.remove('loading');
      this.sendButton.querySelector('.send-text').textContent = 'Send';
      this.typingIndicator.style.display = 'none';
      this.handleInputChange(); // Re-evaluate button state
    }
  }

  scrollToBottom() {
    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
  }

  async checkServerStatus() {
    try {
      const response = await fetch('/health', { method: 'GET' });
      const isHealthy = response.ok;
      
      this.updateConnectionStatus(isHealthy);
      
      // Check again in 30 seconds
      setTimeout(() => this.checkServerStatus(), 30000);
      
    } catch (error) {
      console.warn('Health check failed:', error);
      this.updateConnectionStatus(false);
      
      // Retry sooner if disconnected
      setTimeout(() => this.checkServerStatus(), 10000);
    }
  }

  updateConnectionStatus(connected) {
    this.isConnected = connected;
    
    if (connected) {
      this.statusDot.className = 'status-dot';
      this.statusText.textContent = 'Connected';
    } else {
      this.statusDot.className = 'status-dot error';
      this.statusText.textContent = 'Disconnected';
    }
  }

  showErrorModal(title, message) {
    this.errorMessage.textContent = message;
    this.errorModal.style.display = 'flex';
  }

  hideErrorModal() {
    this.errorModal.style.display = 'none';
  }

  clearChat() {
    if (confirm('Are you sure you want to clear the chat history?')) {
      // Remove all messages except welcome message
      const messages = this.messagesContainer.querySelectorAll('.message');
      messages.forEach(msg => msg.remove());
      
      // Clear history
      this.messageHistory = [];
      this.saveChatHistory();
      
      // Generate new session ID
      this.sessionId = this.generateSessionId();
    }
  }

  saveChatHistory() {
    try {
      localStorage.setItem('vaultMcpChatHistory', JSON.stringify(this.messageHistory));
    } catch (error) {
      console.warn('Failed to save chat history:', error);
    }
  }

  loadChatHistory() {
    try {
      const saved = localStorage.getItem('vaultMcpChatHistory');
      if (saved) {
        this.messageHistory = JSON.parse(saved);
        
        // Restore messages (limit to last 20 for performance)
        const recentMessages = this.messageHistory.slice(-20);
        recentMessages.forEach(msg => {
          this.addMessageFromHistory(msg);
        });
      }
    } catch (error) {
      console.warn('Failed to load chat history:', error);
    }
  }

  addMessageFromHistory(msg) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${msg.sender}`;
    
    const timestamp = new Date(msg.timestamp).toLocaleTimeString();
    const senderName = msg.sender === 'user' ? 'You' : 'AI Assistant';
    
    let messageHTML = `
      <div class="message-header">${senderName}</div>
      <div class="message-content">${this.formatMessageContent(msg.content)}</div>
      <div class="message-footer">
        <span class="timestamp">${timestamp}</span>
    `;
    
    if (msg.metadata?.toolsUsed && msg.metadata.toolsUsed.length > 0) {
      const toolBadges = msg.metadata.toolsUsed.map(tool => 
        `<span class="tool-badge">${tool}</span>`
      ).join('');
      messageHTML += `<div class="tools-used">${toolBadges}</div>`;
    }
    
    messageHTML += '</div>';
    messageDiv.innerHTML = messageHTML;
    
    if (msg.metadata?.error) {
      messageDiv.style.borderLeft = '4px solid #ef4444';
      messageDiv.style.background = '#fef2f2';
    }
    
    this.messagesContainer.appendChild(messageDiv);
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.vaultMcpClient = new VaultMCPClient();
});
