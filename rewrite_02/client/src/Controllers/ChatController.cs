using Microsoft.AspNetCore.Mvc;
using Vault.MCP.Shared.DTOs;
using Vault.MCP.Shared.Interfaces;

namespace Vault.MCP.Client.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class ChatController : ControllerBase
{
    private readonly IMcpClientService _mcpClientService;
    private readonly ILogger<ChatController> _logger;

    public ChatController(IMcpClientService mcpClientService, ILogger<ChatController> logger)
    {
        _mcpClientService = mcpClientService;
        _logger = logger;
    }

    /// <summary>
    /// Process a chat message and return AI-generated response using available tools
    /// </summary>
    /// <param name="request">Chat request with user message</param>
    /// <returns>AI response with tool execution results</returns>
    [HttpPost]
    public async Task<IActionResult> ProcessChat([FromBody] ChatRequestDto request)
    {
        if (string.IsNullOrEmpty(request.Message))
        {
            return BadRequest(new { error = "Message is required" });
        }

        try
        {
            var response = await _mcpClientService.ProcessChatRequestAsync(request);
            
            if (response.Success)
            {
                return Ok(response);
            }
            else
            {
                return BadRequest(response);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing chat request");
            return StatusCode(500, new ChatResponseDto
            {
                Success = false,
                Error = "Failed to process chat request",
                Reply = "I'm sorry, but I encountered an error while processing your request. Please try again."
            });
        }
    }

    /// <summary>
    /// Get available tools from the MCP server
    /// </summary>
    /// <returns>List of available tools</returns>
    [HttpGet("tools")]
    public async Task<IActionResult> GetTools()
    {
        try
        {
            var tools = await _mcpClientService.GetAvailableToolsAsync();
            return Ok(tools);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tools");
            return StatusCode(500, new { error = "Failed to retrieve tools" });
        }
    }

    /// <summary>
    /// Execute a specific tool directly
    /// </summary>
    /// <param name="request">Tool execution request</param>
    /// <returns>Tool execution result</returns>
    [HttpPost("tool")]
    public async Task<IActionResult> ExecuteTool([FromBody] ToolRequestDto request)
    {
        if (string.IsNullOrEmpty(request.Tool))
        {
            return BadRequest(new { error = "Tool name is required" });
        }

        try
        {
            var result = await _mcpClientService.ExecuteToolAsync(request);
            
            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing tool {ToolName}", request.Tool);
            return StatusCode(500, new { error = "Tool execution failed" });
        }
    }
}

// Legacy endpoints for backward compatibility
[ApiController]
[Route("/")]
public class LegacyChatController : ControllerBase
{
    private readonly IMcpClientService _mcpClientService;

    public LegacyChatController(IMcpClientService mcpClientService)
    {
        _mcpClientService = mcpClientService;
    }

    [HttpPost("ask")]
    public async Task<IActionResult> AskLegacy([FromBody] LegacyAskRequest request)
    {
        var chatRequest = new ChatRequestDto { Message = request.Message };
        var response = await _mcpClientService.ProcessChatRequestAsync(chatRequest);
        
        // Return in legacy format
        return Ok(new { reply = response.Reply });
    }

    [HttpGet("tools")]
    public async Task<IActionResult> GetToolsLegacy()
    {
        var tools = await _mcpClientService.GetAvailableToolsAsync();
        return Ok(tools);
    }

    [HttpPost("tool")]
    public async Task<IActionResult> ExecuteToolLegacy([FromBody] ToolRequestDto request)
    {
        var result = await _mcpClientService.ExecuteToolAsync(request);
        return Ok(result);
    }
}

public class LegacyAskRequest
{
    public string Message { get; set; } = string.Empty;
}
