using Microsoft.Extensions.Diagnostics.HealthChecks;
using Vault.MCP.Shared.Interfaces;

namespace Vault.MCP.Client.Services;

public class McpServerHealthCheck : IHealthCheck
{
    private readonly IMcpClientService _mcpClientService;
    private readonly ILogger<McpServerHealthCheck> _logger;

    public McpServerHealthCheck(IMcpClientService mcpClientService, ILogger<McpServerHealthCheck> logger)
    {
        _mcpClientService = mcpClientService;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Performing MCP Server health check");
            
            var tools = await _mcpClientService.GetAvailableToolsAsync();
            
            if (tools.Any())
            {
                _logger.LogDebug("MCP Server health check passed - {ToolCount} tools available", tools.Count);
                return HealthCheckResult.Healthy($"MCP Server is responsive with {tools.Count} tools available");
            }
            else
            {
                _logger.LogWarning("MCP Server health check failed - no tools available");
                return HealthCheckResult.Degraded("MCP Server is responsive but no tools are available");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MCP Server health check failed with exception");
            return HealthCheckResult.Unhealthy("MCP Server health check failed", ex);
        }
    }
}

public class AiServiceHealthCheck : IHealthCheck
{
    private readonly IAiService _aiService;
    private readonly ILogger<AiServiceHealthCheck> _logger;

    public AiServiceHealthCheck(IAiService aiService, ILogger<AiServiceHealthCheck> logger)
    {
        _aiService = aiService;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Performing AI Service health check");
            
            // Simple test prompt
            var response = await _aiService.GenerateResponseAsync("Hello, are you working?");
            
            if (!string.IsNullOrEmpty(response))
            {
                _logger.LogDebug("AI Service health check passed");
                return HealthCheckResult.Healthy("AI Service is responsive");
            }
            else
            {
                _logger.LogWarning("AI Service health check failed - empty response");
                return HealthCheckResult.Degraded("AI Service returned empty response");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AI Service health check failed with exception");
            return HealthCheckResult.Unhealthy("AI Service health check failed", ex);
        }
    }
}
