using Microsoft.Extensions.Options;
using System.Text.Json;
using Vault.MCP.Shared.DTOs;
using Vault.MCP.Shared.Extensions;
using Vault.MCP.Shared.Interfaces;
using Vault.MCP.Shared.Models;

namespace Vault.MCP.Client.Services;

public class AiService : IAiService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<AiService> _logger;
    private readonly AiApiConfiguration _config;

    public AiService(IHttpClientFactory httpClientFactory, ILogger<AiService> logger, IOptions<AiApiConfiguration> config)
    {
        _httpClient = httpClientFactory.CreateClient("ai-api");
        _logger = logger;
        _config = config.Value;
    }

    public async Task<string> GenerateResponseAsync(string prompt, string? context = null)
    {
        try
        {
            var messages = new List<AiMessageDto>
            {
                new() { Role = "system", Content = "You are a helpful assistant that helps users interact with Autodesk Vault through available tools." }
            };

            if (!string.IsNullOrEmpty(context))
            {
                messages.Add(new AiMessageDto { Role = "system", Content = $"Context: {context}" });
            }

            messages.Add(new AiMessageDto { Role = "user", Content = prompt });

            var request = new AiRequestDto
            {
                Model = _config.DefaultModel,
                Messages = messages,
                Temperature = _config.Temperature,
                MaxTokens = _config.MaxTokens
            };

            _logger.LogDebug("Sending AI request with {MessageCount} messages", messages.Count);

            var response = await _httpClient.PostAsJsonAsync<AiRequestDto, AiResponseDto>("/chat/completions", request);

            if (response?.Choices?.Any() == true)
            {
                var content = response.Choices[0].Message.Content;
                _logger.LogDebug("AI response received: {Length} characters", content.Length);
                return content;
            }

            throw new InvalidOperationException("AI service returned empty response");
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error while calling AI service");
            throw new InvalidOperationException($"Failed to connect to AI service: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout while calling AI service");
            throw new TimeoutException($"AI service request timed out after {_config.TimeoutSeconds} seconds", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error while calling AI service");
            throw;
        }
    }

    public async Task<List<PlanStepDto>> GeneratePlanAsync(string userMessage, List<Tool> availableTools)
    {
        try
        {
            var toolDescriptions = string.Join(" | ", availableTools.Select(t => $"{t.Name}: {t.Description}"));
            var prompt = $@"Given the following tools: {toolDescriptions}. 
The user message is: '{userMessage}'. 
Respond with a JSON array, where each element is an object with:
- 'tool' (tool name to use)
- 'params' (object with parameters for the tool)
- 'description' (brief description of what this step does)

You can use multiple tools in sequence if needed to fully answer the user's request. 
Always answer the user's original question, using intermediate tool results as needed. 
Respond with only valid JSON, no comments or explanations.";

            var aiResponse = await GenerateResponseAsync(prompt);
            
            try
            {
                var plan = JsonSerializer.Deserialize<List<PlanStepDto>>(aiResponse, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                if (plan == null || !plan.Any())
                {
                    _logger.LogWarning("AI returned empty plan for user message: {UserMessage}", userMessage);
                    return new List<PlanStepDto>();
                }

                _logger.LogInformation("Generated plan with {StepCount} steps", plan.Count);
                return plan;
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Failed to parse AI plan response: {Response}", aiResponse);
                throw new InvalidOperationException("AI service returned invalid plan format", ex);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating plan for user message: {UserMessage}", userMessage);
            throw;
        }
    }

    public async Task<string> GenerateFinalAnswerAsync(string userMessage, List<object> toolResults)
    {
        try
        {
            var resultsJson = JsonSerializer.Serialize(toolResults, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            });

            var prompt = $@"You have the following tool results: {resultsJson}. 
The original user message was: '{userMessage}'. 
Based on these results, provide a helpful and informative answer to the user's question. 
Be specific and include relevant details from the tool results.
If the results don't fully answer the question, explain what information is available and what might be missing.";

            var finalAnswer = await GenerateResponseAsync(prompt);
            
            _logger.LogInformation("Generated final answer for user message: {UserMessage}", userMessage);
            return finalAnswer;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating final answer for user message: {UserMessage}", userMessage);
            throw;
        }
    }
}
