using Microsoft.Extensions.Options;
using Vault.MCP.Shared.DTOs;
using Vault.MCP.Shared.Extensions;
using Vault.MCP.Shared.Interfaces;
using Vault.MCP.Shared.Models;

namespace Vault.MCP.Client.Services;

public class McpClientService : IMcpClientService
{
    private readonly HttpClient _httpClient;
    private readonly IAiService _aiService;
    private readonly ISessionService _sessionService;
    private readonly ILogger<McpClientService> _logger;
    private readonly McpServerConfiguration _config;

    public McpClientService(
        IHttpClientFactory httpClientFactory, 
        IAiService aiService,
        ISessionService sessionService,
        ILogger<McpClientService> logger, 
        IOptions<McpServerConfiguration> config)
    {
        _httpClient = httpClientFactory.CreateClient("mcp-server");
        _aiService = aiService;
        _sessionService = sessionService;
        _logger = logger;
        _config = config.Value;
    }

    public async Task<ChatResponseDto> ProcessChatRequestAsync(ChatRequestDto request)
    {
        var sessionId = request.SessionId ?? Guid.NewGuid().ToString();
        
        try
        {
            _logger.LogInformation("Processing chat request for session {SessionId}: {Message}", sessionId, request.Message);

            // Get available tools
            var tools = await GetAvailableToolsAsync();
            if (!tools.Any())
            {
                return new ChatResponseDto
                {
                    Reply = "I'm sorry, but no tools are currently available to help with your request.",
                    SessionId = sessionId,
                    Success = false,
                    Error = "No tools available"
                };
            }

            // Generate execution plan
            var plan = await _aiService.GeneratePlanAsync(request.Message, tools);
            if (!plan.Any())
            {
                return new ChatResponseDto
                {
                    Reply = "I'm not sure how to help with that request. Could you please rephrase or be more specific?",
                    SessionId = sessionId,
                    Success = false,
                    Error = "No execution plan generated"
                };
            }

            // Execute plan steps
            var toolResults = new List<object>();
            var toolsUsed = new List<string>();

            foreach (var step in plan)
            {
                try
                {
                    _logger.LogDebug("Executing step: {Tool} - {Description}", step.Tool, step.Description);
                    
                    var toolRequest = new ToolRequestDto
                    {
                        Tool = step.Tool,
                        Params = step.Params
                    };

                    var result = await ExecuteToolAsync(toolRequest);
                    toolResults.Add(result);
                    toolsUsed.Add(step.Tool);

                    if (!result.Success)
                    {
                        _logger.LogWarning("Tool execution failed: {Tool} - {Error}", step.Tool, result.Error);
                        // Continue with other steps, but note the failure
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error executing tool {Tool}", step.Tool);
                    toolResults.Add(new ToolResponseDto
                    {
                        Success = false,
                        Error = $"Tool execution failed: {ex.Message}",
                        Timestamp = DateTime.UtcNow
                    });
                }
            }

            // Generate final answer
            var finalAnswer = await _aiService.GenerateFinalAnswerAsync(request.Message, toolResults);

            // Store session context
            await _sessionService.UpdateSessionContextAsync(sessionId, new
            {
                lastMessage = request.Message,
                toolsUsed = toolsUsed,
                timestamp = DateTime.UtcNow
            });

            var response = new ChatResponseDto
            {
                Reply = finalAnswer,
                SessionId = sessionId,
                ToolsUsed = toolsUsed,
                Success = true
            };

            _logger.LogInformation("Chat request processed successfully for session {SessionId}", sessionId);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing chat request for session {SessionId}", sessionId);
            return new ChatResponseDto
            {
                Reply = "I'm sorry, but I encountered an error while processing your request. Please try again.",
                SessionId = sessionId,
                Success = false,
                Error = ex.Message
            };
        }
    }

    public async Task<List<Tool>> GetAvailableToolsAsync()
    {
        try
        {
            _logger.LogDebug("Retrieving available tools from MCP server");
            var tools = await _httpClient.GetFromJsonAsync<List<Tool>>("/tools");
            
            if (tools == null)
            {
                _logger.LogWarning("MCP server returned null tools list");
                return new List<Tool>();
            }

            _logger.LogDebug("Retrieved {ToolCount} tools from MCP server", tools.Count);
            return tools;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error while retrieving tools from MCP server");
            throw new InvalidOperationException($"Failed to connect to MCP server: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout while retrieving tools from MCP server");
            throw new TimeoutException($"MCP server request timed out after {_config.TimeoutSeconds} seconds", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error while retrieving tools from MCP server");
            throw;
        }
    }

    public async Task<ToolResponseDto> ExecuteToolAsync(ToolRequestDto request)
    {
        try
        {
            _logger.LogDebug("Executing tool {ToolName} on MCP server", request.Tool);
            
            var response = await _httpClient.PostAsJsonAsync<ToolRequestDto, ToolResponseDto>("/tool", request);
            
            if (response == null)
            {
                throw new InvalidOperationException("MCP server returned null response");
            }

            _logger.LogDebug("Tool {ToolName} executed with success: {Success}", request.Tool, response.Success);
            return response;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error while executing tool {ToolName}", request.Tool);
            throw new InvalidOperationException($"Failed to connect to MCP server: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout while executing tool {ToolName}", request.Tool);
            throw new TimeoutException($"MCP server request timed out after {_config.TimeoutSeconds} seconds", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error while executing tool {ToolName}", request.Tool);
            throw;
        }
    }
}
