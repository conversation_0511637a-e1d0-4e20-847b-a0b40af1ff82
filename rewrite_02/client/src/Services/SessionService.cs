using Microsoft.Extensions.Caching.Memory;

namespace Vault.MCP.Client.Services;

public interface ISessionService
{
    Task<T?> GetSessionDataAsync<T>(string sessionId, string key);
    Task SetSessionDataAsync<T>(string sessionId, string key, T value, TimeSpan? expiration = null);
    Task UpdateSessionContextAsync(string sessionId, object context);
    Task<object?> GetSessionContextAsync(string sessionId);
    Task ClearSessionAsync(string sessionId);
}

public class SessionService : ISessionService
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<SessionService> _logger;
    private readonly TimeSpan _defaultExpiration = TimeSpan.FromHours(1);

    public SessionService(IMemoryCache cache, ILogger<SessionService> logger)
    {
        _cache = cache;
        _logger = logger;
    }

    public Task<T?> GetSessionDataAsync<T>(string sessionId, string key)
    {
        try
        {
            var cacheKey = $"session:{sessionId}:{key}";
            var value = _cache.Get<T>(cacheKey);
            
            _logger.LogDebug("Retrieved session data for {SessionId}:{Key} - Found: {Found}", sessionId, key, value != null);
            return Task.FromResult(value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving session data for {SessionId}:{Key}", sessionId, key);
            return Task.FromResult(default(T));
        }
    }

    public Task SetSessionDataAsync<T>(string sessionId, string key, T value, TimeSpan? expiration = null)
    {
        try
        {
            var cacheKey = $"session:{sessionId}:{key}";
            var options = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = expiration ?? _defaultExpiration,
                SlidingExpiration = TimeSpan.FromMinutes(30) // Extend if accessed
            };

            _cache.Set(cacheKey, value, options);
            
            _logger.LogDebug("Stored session data for {SessionId}:{Key}", sessionId, key);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing session data for {SessionId}:{Key}", sessionId, key);
            return Task.CompletedTask;
        }
    }

    public async Task UpdateSessionContextAsync(string sessionId, object context)
    {
        await SetSessionDataAsync(sessionId, "context", context);
    }

    public async Task<object?> GetSessionContextAsync(string sessionId)
    {
        return await GetSessionDataAsync<object>(sessionId, "context");
    }

    public Task ClearSessionAsync(string sessionId)
    {
        try
        {
            // In a real implementation, you might want to iterate through all keys for this session
            // For now, we'll clear the main context
            var contextKey = $"session:{sessionId}:context";
            _cache.Remove(contextKey);
            
            _logger.LogDebug("Cleared session data for {SessionId}", sessionId);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing session data for {SessionId}", sessionId);
            return Task.CompletedTask;
        }
    }
}
