{"Version": 1, "Hash": "TGkZOsWIbVnftLBu++3BeQ9XtmIlwJcKq0f+MEL+La4=", "Source": "Vault.MCP.Client", "BasePath": "_content/Vault.MCP.Client", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Vault.MCP.Client/wwwroot", "Source": "Vault.MCP.Client", "ContentRoot": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/wwwroot/", "BasePath": "_content/Vault.MCP.Client", "Pattern": "**"}], "Assets": [{"Identity": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/wwwroot/app.js", "SourceId": "Vault.MCP.Client", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/wwwroot/", "BasePath": "_content/Vault.MCP.Client", "RelativePath": "app.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/app.js"}, {"Identity": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/wwwroot/index.html", "SourceId": "Vault.MCP.Client", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/wwwroot/", "BasePath": "_content/Vault.MCP.Client", "RelativePath": "index.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/index.html"}, {"Identity": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/wwwroot/styles.css", "SourceId": "Vault.MCP.Client", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/wwwroot/", "BasePath": "_content/Vault.MCP.Client", "RelativePath": "styles.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/styles.css"}]}