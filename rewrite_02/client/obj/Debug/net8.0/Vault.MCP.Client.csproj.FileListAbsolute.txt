/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/appsettings.Development.json
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/appsettings.json
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Vault.MCP.Client.staticwebassets.runtime.json
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Vault.MCP.Client
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Vault.MCP.Client.deps.json
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Vault.MCP.Client.runtimeconfig.json
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Vault.MCP.Client.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Vault.MCP.Client.pdb
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.AspNetCore.OpenApi.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.Extensions.Caching.Memory.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Binder.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Json.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.Extensions.DependencyInjection.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.Extensions.Diagnostics.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.Extensions.Http.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.Extensions.Logging.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.Extensions.Logging.Abstractions.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.Extensions.Logging.Configuration.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.Extensions.Logging.Console.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.Extensions.Logging.Debug.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.Extensions.Options.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Microsoft.OpenApi.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Swashbuckle.AspNetCore.Swagger.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Vault.MCP.Shared.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/bin/Debug/net8.0/Vault.MCP.Shared.pdb
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/Vault.MCP.Client.csproj.AssemblyReference.cache
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/Vault.MCP.Client.GeneratedMSBuildEditorConfig.editorconfig
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/Vault.MCP.Client.AssemblyInfoInputs.cache
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/Vault.MCP.Client.AssemblyInfo.cs
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/Vault.MCP.Client.csproj.CoreCompileInputs.cache
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/Vault.MCP.Client.MvcApplicationPartsAssemblyInfo.cs
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/Vault.MCP.Client.MvcApplicationPartsAssemblyInfo.cache
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/staticwebassets.build.json
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/staticwebassets.development.json
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/staticwebassets/msbuild.Vault.MCP.Client.Microsoft.AspNetCore.StaticWebAssets.props
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/staticwebassets/msbuild.build.Vault.MCP.Client.props
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/staticwebassets/msbuild.buildMultiTargeting.Vault.MCP.Client.props
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/staticwebassets/msbuild.buildTransitive.Vault.MCP.Client.props
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/staticwebassets.pack.json
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/scopedcss/bundle/Vault.MCP.Client.styles.css
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/Vault.MC.C60E0A62.Up2Date
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/Vault.MCP.Client.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/refint/Vault.MCP.Client.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/Vault.MCP.Client.pdb
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/Vault.MCP.Client.genruntimeconfig.cache
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/Debug/net8.0/ref/Vault.MCP.Client.dll
