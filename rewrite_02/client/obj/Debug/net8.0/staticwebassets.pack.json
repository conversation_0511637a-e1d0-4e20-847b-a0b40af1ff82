{"Files": [{"Id": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/wwwroot/app.js", "PackagePath": "staticwebassets/app.js"}, {"Id": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/wwwroot/index.html", "PackagePath": "staticwebassets/index.html"}, {"Id": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/wwwroot/styles.css", "PackagePath": "staticwebassets/styles.css"}, {"Id": "obj/Debug/net8.0/staticwebassets/msbuild.Vault.MCP.Client.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj/Debug/net8.0/staticwebassets/msbuild.build.Vault.MCP.Client.props", "PackagePath": "build\\Vault.MCP.Client.props"}, {"Id": "obj/Debug/net8.0/staticwebassets/msbuild.buildMultiTargeting.Vault.MCP.Client.props", "PackagePath": "buildMultiTargeting\\Vault.MCP.Client.props"}, {"Id": "obj/Debug/net8.0/staticwebassets/msbuild.buildTransitive.Vault.MCP.Client.props", "PackagePath": "buildTransitive\\Vault.MCP.Client.props"}], "ElementsToRemove": []}