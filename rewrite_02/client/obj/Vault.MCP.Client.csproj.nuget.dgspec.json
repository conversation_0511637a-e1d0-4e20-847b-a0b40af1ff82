{"format": 1, "restore": {"/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/Vault.MCP.Client.csproj": {}}, "projects": {"/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/Vault.MCP.Client.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/Vault.MCP.Client.csproj", "projectName": "Vault.MCP.Client", "projectPath": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/Vault.MCP.Client.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/client/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/shared/Vault.MCP.Shared.csproj": {"projectPath": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/shared/Vault.MCP.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Cors": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.14, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.1, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/shared/Vault.MCP.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/shared/Vault.MCP.Shared.csproj", "projectName": "Vault.MCP.Shared", "projectPath": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/shared/Vault.MCP.Shared.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/shared/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[8.0.1, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}}}