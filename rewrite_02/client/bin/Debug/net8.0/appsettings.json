{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.AspNetCore.Hosting": "Information", "Vault.MCP.Client": "Debug"}}, "AllowedHosts": "*", "AiApi": {"BaseUrl": "https://api.epicai.fun/v1", "ApiKey": "ecs-00", "DefaultModel": "gpt-3.5-turbo", "Temperature": 0.7, "MaxTokens": 1000, "TimeoutSeconds": 120}, "McpServer": {"BaseUrl": "http://localhost:5001", "TimeoutSeconds": 30, "EnableHealthCheck": true, "HealthCheckIntervalSeconds": 60}, "McpClient": {"BaseUrl": "http://localhost:5098", "TimeoutSeconds": 30, "EnableCors": true, "AllowedOrigins": ["*"]}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://localhost:5098"}}}}