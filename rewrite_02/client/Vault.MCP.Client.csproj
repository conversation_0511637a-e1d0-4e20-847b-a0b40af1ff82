<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <LangVersion>latest</LangVersion>
    <AssemblyName>Vault.MCP.Client</AssemblyName>
    <RootNamespace>Vault.MCP.Client</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.14" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Cors" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../shared/Vault.MCP.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="src/Controllers/" />
    <Folder Include="src/Services/" />
    <Folder Include="src/Middleware/" />
    <Folder Include="wwwroot/" />
    <Folder Include="tests/" />
  </ItemGroup>

</Project>
