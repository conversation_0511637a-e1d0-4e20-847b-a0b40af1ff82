using Vault.MCP.Client.Services;
using Vault.MCP.Client.Middleware;
using Vault.MCP.Shared.Interfaces;
using Vault.MCP.Shared.Models;

var builder = WebApplication.CreateBuilder(args);

// Add configuration
builder.Configuration.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
builder.Configuration.AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true);
builder.Configuration.AddEnvironmentVariables();

// Configure strongly typed settings
builder.Services.Configure<ApplicationConfiguration>(builder.Configuration);
builder.Services.Configure<AiApiConfiguration>(builder.Configuration.GetSection("AiApi"));
builder.Services.Configure<McpServerConfiguration>(builder.Configuration.GetSection("McpServer"));
builder.Services.Configure<McpClientConfiguration>(builder.Configuration.GetSection("McpClient"));

// Add services to the container
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
        options.JsonSerializerOptions.WriteIndented = true;
    });

// Add API documentation
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { 
        Title = "Vault MCP Client API", 
        Version = "v1",
        Description = "MCP Client for AI-powered Vault interactions with improved error handling and session management"
    });
});

// Add HTTP clients
builder.Services.AddHttpClient("default", client =>
{
    client.Timeout = TimeSpan.FromSeconds(30);
    client.DefaultRequestHeaders.Add("User-Agent", "Vault-MCP-Client/1.0");
});

builder.Services.AddHttpClient("ai-api", client =>
{
    var baseUrl = builder.Configuration.GetValue<string>("AiApi:BaseUrl") ?? "https://api.epicai.fun/v1";
    client.BaseAddress = new Uri(baseUrl);
    client.Timeout = TimeSpan.FromSeconds(120);

    var apiKey = builder.Configuration.GetValue<string>("AiApi:ApiKey");
    if (!string.IsNullOrEmpty(apiKey))
    {
        client.DefaultRequestHeaders.Authorization =
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", apiKey);
    }
});

builder.Services.AddHttpClient("mcp-server", client =>
{
    var baseUrl = builder.Configuration.GetValue<string>("McpServer:BaseUrl") ?? "http://localhost:5001";
    client.BaseAddress = new Uri(baseUrl);
    client.Timeout = TimeSpan.FromSeconds(30);
});

// Add memory cache for session management
builder.Services.AddMemoryCache();

// Add application services
builder.Services.AddScoped<IAiService, AiService>();
builder.Services.AddScoped<IMcpClientService, McpClientService>();
builder.Services.AddScoped<ISessionService, SessionService>();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

// Add health checks
builder.Services.AddHealthChecks()
    .AddCheck<McpServerHealthCheck>("mcp-server")
    .AddCheck<AiServiceHealthCheck>("ai-service");

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Vault MCP Client API v1");
        c.RoutePrefix = string.Empty; // Serve Swagger UI at root
    });
}

// Add custom middleware
app.UseMiddleware<ErrorHandlingMiddleware>();
app.UseMiddleware<RequestLoggingMiddleware>();

app.UseHttpsRedirection();
app.UseCors();

// Serve static files
app.UseDefaultFiles();
app.UseStaticFiles();

app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/health");

// Add a simple root endpoint
app.MapGet("/api", () => new { 
    service = "Vault MCP Client", 
    version = "2.0", 
    status = "running",
    timestamp = DateTime.UtcNow 
});

app.Run();
