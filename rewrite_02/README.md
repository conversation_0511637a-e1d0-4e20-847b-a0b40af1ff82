# Vault MCP AI - Rewrite 02

A modern, refactored implementation of the Vault MCP (Model Context Protocol) system with improved architecture, error handling, and user experience.

## 🚀 Features

- **Modern .NET 8 Architecture**: Clean separation of concerns with dependency injection
- **Enhanced Error Handling**: Comprehensive error handling and logging throughout the system
- **Improved UI/UX**: Modern, responsive web interface with real-time status indicators
- **Session Management**: Persistent chat sessions with memory caching
- **Health Monitoring**: Built-in health checks for all services
- **Configuration Management**: Flexible configuration with environment-specific settings
- **API Documentation**: Swagger/OpenAPI documentation for all endpoints
- **Shared Models**: Consistent data models across server and client

## 📁 Project Structure

```
rewrite_02/
├── shared/                 # Shared models, DTOs, and interfaces
│   ├── Models/            # Domain models (Tool, VaultFile, Configuration)
│   ├── DTOs/              # Data transfer objects
│   ├── Interfaces/        # Service interfaces
│   └── Extensions/        # Extension methods and utilities
├── server/                # MCP Server implementation
│   ├── src/
│   │   ├── Controllers/   # API controllers
│   │   ├── Services/      # Business logic services
│   │   └── Middleware/    # Custom middleware
│   └── tests/             # Unit tests
├── client/                # MCP Client implementation
│   ├── src/
│   │   ├── Controllers/   # API controllers
│   │   ├── Services/      # Business logic services
│   │   └── Middleware/    # Custom middleware
│   ├── wwwroot/           # Static web files
│   └── tests/             # Unit tests
├── docs/                  # Documentation
└── scripts/               # Build and deployment scripts
```

## 🛠️ Prerequisites

- [.NET 8 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
- [Node.js](https://nodejs.org/) (for the mock Vault API)
- A text editor or IDE (Visual Studio, VS Code, Rider)

## 🚀 Quick Start

### 1. Clone and Navigate
```bash
cd rewrite_02
```

### 2. Start the Mock Vault API
```bash
# In a separate terminal, start the mock API
cd ../vault-mock-api
node index.js
```

### 3. Start the MCP Server
```bash
# Build and run the server
cd server
dotnet restore
dotnet run
```
The server will be available at `http://localhost:5001`

### 4. Start the MCP Client
```bash
# In another terminal, build and run the client
cd client
dotnet restore
dotnet run
```
The client will be available at `http://localhost:5098`

### 5. Access the Application
- **Web Interface**: http://localhost:5098
- **Server API Docs**: http://localhost:5001
- **Client API Docs**: http://localhost:5098/swagger
- **Health Checks**: 
  - Server: http://localhost:5001/health
  - Client: http://localhost:5098/health

## 🔧 Configuration

### Server Configuration (`server/appsettings.json`)
```json
{
  "VaultApi": {
    "BaseUrl": "http://localhost:4000",
    "DefaultVaultId": "117",
    "TimeoutSeconds": 60
  },
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://localhost:5001"
      }
    }
  }
}
```

### Client Configuration (`client/appsettings.json`)
```json
{
  "AiApi": {
    "BaseUrl": "https://api.epicai.fun/v1",
    "ApiKey": "ecs-00",
    "DefaultModel": "gpt-3.5-turbo"
  },
  "McpServer": {
    "BaseUrl": "http://localhost:5001"
  },
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://localhost:5098"
      }
    }
  }
}
```

## 🔌 Available Tools

### 1. getTime
Returns the current server time in ISO 8601 format.

**Parameters**: None

**Example**:
```json
{
  "tool": "getTime",
  "params": {}
}
```

### 2. getVaultFiles
Retrieves files from Autodesk Vault with filtering and sorting options.

**Parameters**:
- `vaultId` (string, optional): Vault ID (default: "117")
- `filter_State` (string, optional): Filter by file state
- `option_latestOnly` (boolean, optional): Only latest version (default: true)
- `option_releasedFilesOnly` (boolean, optional): Only released files (default: false)
- `sort` (string, optional): Sort criteria (e.g., "Revision desc,Name asc")
- `limit` (number, optional): Number of results (default: 10, max: 100)

**Example**:
```json
{
  "tool": "getVaultFiles",
  "params": {
    "vaultId": "117",
    "option_releasedFilesOnly": true,
    "limit": 5
  }
}
```

## 🧪 Testing

### Run Unit Tests
```bash
# Server tests
cd server
dotnet test

# Client tests
cd client
dotnet test
```

### Manual Testing
1. **Health Checks**: Visit `/health` endpoints to verify service status
2. **API Documentation**: Use Swagger UI to test individual endpoints
3. **Web Interface**: Use the chat interface to test end-to-end functionality

## 🐛 Troubleshooting

### Common Issues

1. **Port Conflicts**
   - Server default: 5001
   - Client default: 5098
   - Mock API default: 4000
   
   Update `appsettings.json` if ports are in use.

2. **AI API Connection Issues**
   - Verify the AI API key in `client/appsettings.json`
   - Check network connectivity to the AI service

3. **Vault API Connection Issues**
   - Ensure the mock Vault API is running on port 4000
   - Check the `VaultApi.BaseUrl` configuration

### Logs
Both server and client provide detailed logging:
- **Console**: Real-time logs during development
- **Debug**: Additional debug information in Development environment

### Health Checks
Monitor service health at:
- Server: `GET /health`
- Client: `GET /health`

## 🔄 API Endpoints

### Server Endpoints
- `GET /tools` - Get available tools
- `POST /tool` - Execute a tool
- `GET /health` - Health check
- `GET /` - Service info

### Client Endpoints
- `POST /ask` - Process chat message (legacy)
- `POST /api/chat` - Process chat message (new)
- `GET /api/chat/tools` - Get available tools
- `POST /api/chat/tool` - Execute tool directly
- `GET /health` - Health check

## 🚀 Deployment

### Development
Use `dotnet run` for development with hot reload.

### Production
1. Build the applications:
   ```bash
   dotnet publish -c Release
   ```

2. Configure production settings in `appsettings.Production.json`

3. Deploy to your preferred hosting platform (IIS, Docker, Azure, etc.)

## 📝 Contributing

1. Follow the existing code structure and patterns
2. Add unit tests for new functionality
3. Update documentation for any API changes
4. Use the shared models and interfaces for consistency

## 📄 License

This project is part of the Vault MCP AI system. See the main repository for license information.
