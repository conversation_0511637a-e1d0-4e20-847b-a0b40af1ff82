using Vault.MCP.Server.Services;
using Vault.MCP.Server.Middleware;
using Vault.MCP.Shared.Extensions;
using Vault.MCP.Shared.Interfaces;
using Vault.MCP.Shared.Models;

var builder = WebApplication.CreateBuilder(args);

// Add configuration
builder.Configuration.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
builder.Configuration.AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true);
builder.Configuration.AddEnvironmentVariables();

// Configure strongly typed settings
builder.Services.Configure<ApplicationConfiguration>(builder.Configuration);
builder.Services.Configure<VaultApiConfiguration>(builder.Configuration.GetSection("VaultApi"));

// Add services to the container
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
        options.JsonSerializerOptions.WriteIndented = true;
    });

// Add API documentation
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { 
        Title = "Vault MCP Server API", 
        Version = "v1",
        Description = "MCP Server for Vault integration with improved error handling and configuration"
    });
});

// Add shared services
builder.Services.AddSharedServices(builder.Configuration);
builder.Services.AddLoggingConfiguration();

// Add application services
builder.Services.AddScoped<IToolService, ToolService>();
builder.Services.AddScoped<IVaultService, VaultService>();
builder.Services.AddScoped<IMcpServerService, McpServerService>();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

// Add health checks
builder.Services.AddHealthChecks()
    .AddCheck<VaultHealthCheck>("vault-api");

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Vault MCP Server API v1");
        c.RoutePrefix = string.Empty; // Serve Swagger UI at root
    });
}

// Add custom middleware
app.UseMiddleware<ErrorHandlingMiddleware>();
app.UseMiddleware<RequestLoggingMiddleware>();

app.UseHttpsRedirection();
app.UseCors();

app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/health");

// Add a simple root endpoint
app.MapGet("/", () => new { 
    service = "Vault MCP Server", 
    version = "2.0", 
    status = "running",
    timestamp = DateTime.UtcNow 
});

app.Run();
