/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/Vault.MCP.Server.csproj.AssemblyReference.cache
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/Vault.MCP.Server.GeneratedMSBuildEditorConfig.editorconfig
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/Vault.MCP.Server.AssemblyInfoInputs.cache
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/Vault.MCP.Server.AssemblyInfo.cs
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/Vault.MCP.Server.csproj.CoreCompileInputs.cache
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/Vault.MCP.Server.MvcApplicationPartsAssemblyInfo.cs
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/Vault.MCP.Server.MvcApplicationPartsAssemblyInfo.cache
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/appsettings.Development.json
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/appsettings.json
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Vault.MCP.Server
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Vault.MCP.Server.deps.json
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Vault.MCP.Server.runtimeconfig.json
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Vault.MCP.Server.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Vault.MCP.Server.pdb
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Microsoft.AspNetCore.OpenApi.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Binder.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Json.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Microsoft.Extensions.DependencyInjection.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Microsoft.Extensions.Diagnostics.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Microsoft.Extensions.Http.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Microsoft.Extensions.Logging.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Microsoft.Extensions.Logging.Abstractions.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Microsoft.Extensions.Logging.Configuration.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Microsoft.Extensions.Logging.Console.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Microsoft.Extensions.Logging.Debug.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Microsoft.Extensions.Options.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Microsoft.OpenApi.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Swashbuckle.AspNetCore.Swagger.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Vault.MCP.Shared.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/bin/Debug/net8.0/Vault.MCP.Shared.pdb
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/staticwebassets.build.json
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/staticwebassets.development.json
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/staticwebassets/msbuild.Vault.MCP.Server.Microsoft.AspNetCore.StaticWebAssets.props
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/staticwebassets/msbuild.build.Vault.MCP.Server.props
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/staticwebassets/msbuild.buildMultiTargeting.Vault.MCP.Server.props
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/staticwebassets/msbuild.buildTransitive.Vault.MCP.Server.props
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/staticwebassets.pack.json
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/scopedcss/bundle/Vault.MCP.Server.styles.css
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/Vault.MC.6C6C83AB.Up2Date
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/Vault.MCP.Server.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/refint/Vault.MCP.Server.dll
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/Vault.MCP.Server.pdb
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/Vault.MCP.Server.genruntimeconfig.cache
/Users/<USER>/Documents/TFE/Vault_MCP_AI/rewrite_02/server/obj/Debug/net8.0/ref/Vault.MCP.Server.dll
