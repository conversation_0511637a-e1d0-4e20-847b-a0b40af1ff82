using Microsoft.AspNetCore.Mvc;
using Vault.MCP.Shared.DTOs;
using Vault.MCP.Shared.Interfaces;

namespace Vault.MCP.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class ToolsController : ControllerBase
{
    private readonly IMcpServerService _mcpServerService;
    private readonly ILogger<ToolsController> _logger;

    public ToolsController(IMcpServerService mcpServerService, ILogger<ToolsController> logger)
    {
        _mcpServerService = mcpServerService;
        _logger = logger;
    }

    /// <summary>
    /// Get all available tools
    /// </summary>
    /// <returns>List of available tools</returns>
    [HttpGet]
    public async Task<IActionResult> GetTools()
    {
        try
        {
            var tools = await _mcpServerService.GetToolsAsync();
            return Ok(tools);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tools");
            return StatusCode(500, new { error = "Failed to retrieve tools" });
        }
    }

    /// <summary>
    /// Execute a specific tool
    /// </summary>
    /// <param name="request">Tool execution request</param>
    /// <returns>Tool execution result</returns>
    [HttpPost("execute")]
    public async Task<IActionResult> ExecuteTool([FromBody] ToolRequestDto request)
    {
        if (string.IsNullOrEmpty(request.Tool))
        {
            return BadRequest(new { error = "Tool name is required" });
        }

        try
        {
            var result = await _mcpServerService.UseToolAsync(request);
            
            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing tool {ToolName}", request.Tool);
            return StatusCode(500, new { error = "Tool execution failed" });
        }
    }
}

// Legacy endpoints for backward compatibility
[ApiController]
[Route("/")]
public class LegacyController : ControllerBase
{
    private readonly IMcpServerService _mcpServerService;

    public LegacyController(IMcpServerService mcpServerService)
    {
        _mcpServerService = mcpServerService;
    }

    [HttpGet("tools")]
    public async Task<IActionResult> GetToolsLegacy()
    {
        var tools = await _mcpServerService.GetToolsAsync();
        return Ok(tools);
    }

    [HttpPost("tool")]
    public async Task<IActionResult> ExecuteToolLegacy([FromBody] ToolRequestDto request)
    {
        var result = await _mcpServerService.UseToolAsync(request);
        return Ok(result);
    }
}
