using Microsoft.Extensions.Diagnostics.HealthChecks;
using Vault.MCP.Shared.Interfaces;

namespace Vault.MCP.Server.Services;

public class VaultHealthCheck : IHealthCheck
{
    private readonly IVaultService _vaultService;
    private readonly ILogger<VaultHealthCheck> _logger;

    public VaultHealthCheck(IVaultService vaultService, ILogger<VaultHealthCheck> logger)
    {
        _vaultService = vaultService;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Performing Vault API health check");
            
            var isAvailable = await _vaultService.IsVaultAvailableAsync("117");
            
            if (isAvailable)
            {
                _logger.LogDebug("Vault API health check passed");
                return HealthCheckResult.Healthy("Vault API is responsive");
            }
            else
            {
                _logger.LogWarning("Vault API health check failed - service not available");
                return HealthCheckResult.Degraded("Vault API is not responsive");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Vault API health check failed with exception");
            return HealthCheckResult.Unhealthy("Vault API health check failed", ex);
        }
    }
}
