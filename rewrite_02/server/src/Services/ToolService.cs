using Microsoft.Extensions.Options;
using Vault.MCP.Shared.DTOs;
using Vault.MCP.Shared.Interfaces;
using Vault.MCP.Shared.Models;

namespace Vault.MCP.Server.Services;

public class ToolService : IToolService
{
    private readonly IVaultService _vaultService;
    private readonly ILogger<ToolService> _logger;
    private readonly VaultApiConfiguration _config;

    private static readonly List<Tool> _availableTools = new()
    {
        new Tool
        {
            Name = "getTime",
            Description = "Returns the current server time in ISO 8601 format",
            Parameters = new { }
        },
        new Tool
        {
            Name = "getVaultFiles",
            Description = "Retrieves files from Autodesk Vault with filtering and sorting options",
            Parameters = new ToolParameters
            {
                VaultId = new ToolParameter { Type = "string", Required = false, Description = "Vault ID (default: 117)", Default = "117" },
                FilterState = new ToolParameter { Type = "string", Required = false, Description = "Filter by file state (e.g., 'Released', 'Work in Progress')" },
                OptionLatestOnly = new ToolParameter { Type = "boolean", Required = false, Description = "Only latest version (default: true)", Default = true },
                OptionReleasedFilesOnly = new ToolParameter { Type = "boolean", Required = false, Description = "Only released files (default: false)", Default = false },
                OptionExtendedModels = new ToolParameter { Type = "boolean", Required = false, Description = "Return extended model info (default: false)", Default = false },
                OptionPropDefIds = new ToolParameter { Type = "string", Required = false, Description = "Comma-separated property IDs to return, or 'all'" },
                Sort = new ToolParameter { Type = "string", Required = false, Description = "Sort criteria, e.g. 'Revision desc,Name asc'" },
                Limit = new ToolParameter { Type = "number", Required = false, Description = "Number of results to return (default: 10, max: 100)", Default = 10 },
                CursorState = new ToolParameter { Type = "string", Required = false, Description = "Cursor for pagination" }
            }
        }
    };

    public ToolService(IVaultService vaultService, ILogger<ToolService> logger, IOptions<VaultApiConfiguration> config)
    {
        _vaultService = vaultService;
        _logger = logger;
        _config = config.Value;
    }

    public Task<List<Tool>> GetAvailableToolsAsync()
    {
        _logger.LogInformation("Retrieving {ToolCount} available tools", _availableTools.Count);
        return Task.FromResult(_availableTools);
    }

    public async Task<ToolResponseDto> ExecuteToolAsync(ToolRequestDto request)
    {
        try
        {
            _logger.LogInformation("Executing tool: {ToolName}", request.Tool);

            return request.Tool switch
            {
                "getTime" => await ExecuteGetTimeAsync(),
                "getVaultFiles" => await ExecuteGetVaultFilesAsync(request),
                _ => new ToolResponseDto
                {
                    Success = false,
                    Error = $"Unknown tool: {request.Tool}",
                    Timestamp = DateTime.UtcNow
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing tool {ToolName}", request.Tool);
            return new ToolResponseDto
            {
                Success = false,
                Error = $"Tool execution failed: {ex.Message}",
                Timestamp = DateTime.UtcNow
            };
        }
    }

    public Task<bool> IsToolAvailableAsync(string toolName)
    {
        var isAvailable = _availableTools.Any(t => t.Name.Equals(toolName, StringComparison.OrdinalIgnoreCase));
        _logger.LogDebug("Tool {ToolName} availability: {IsAvailable}", toolName, isAvailable);
        return Task.FromResult(isAvailable);
    }

    private Task<ToolResponseDto> ExecuteGetTimeAsync()
    {
        var result = new ToolResponseDto
        {
            Success = true,
            Data = new { time = DateTime.UtcNow.ToString("O"), timezone = "UTC" },
            Timestamp = DateTime.UtcNow
        };

        _logger.LogDebug("GetTime tool executed successfully");
        return Task.FromResult(result);
    }

    private async Task<ToolResponseDto> ExecuteGetVaultFilesAsync(ToolRequestDto request)
    {
        try
        {
            var vaultRequest = MapToVaultFilesRequest(request.Params);
            var vaultResponse = await _vaultService.GetVaultFilesAsync(vaultRequest);

            var result = new ToolResponseDto
            {
                Success = true,
                Data = vaultResponse,
                Timestamp = DateTime.UtcNow
            };

            _logger.LogInformation("GetVaultFiles tool executed successfully. Retrieved {FileCount} files", vaultResponse.Files.Count);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing GetVaultFiles tool");
            return new ToolResponseDto
            {
                Success = false,
                Error = $"Failed to retrieve vault files: {ex.Message}",
                Timestamp = DateTime.UtcNow
            };
        }
    }

    private VaultFilesRequestDto MapToVaultFilesRequest(Dictionary<string, object>? parameters)
    {
        var request = new VaultFilesRequestDto
        {
            VaultId = _config.DefaultVaultId
        };

        if (parameters == null) return request;

        foreach (var param in parameters)
        {
            switch (param.Key.ToLowerInvariant())
            {
                case "vaultid":
                    request.VaultId = param.Value?.ToString();
                    break;
                case "filter_state":
                    request.FilterState = param.Value?.ToString();
                    break;
                case "option_latestonly":
                    if (bool.TryParse(param.Value?.ToString(), out var latestOnly))
                        request.OptionLatestOnly = latestOnly;
                    break;
                case "option_releasedfilesonly":
                    if (bool.TryParse(param.Value?.ToString(), out var releasedOnly))
                        request.OptionReleasedFilesOnly = releasedOnly;
                    break;
                case "option_extendedmodels":
                    if (bool.TryParse(param.Value?.ToString(), out var extendedModels))
                        request.OptionExtendedModels = extendedModels;
                    break;
                case "option_propdefids":
                    request.OptionPropDefIds = param.Value?.ToString();
                    break;
                case "sort":
                    request.Sort = param.Value?.ToString();
                    break;
                case "limit":
                    if (int.TryParse(param.Value?.ToString(), out var limit))
                        request.Limit = Math.Min(limit, 100); // Cap at 100
                    break;
                case "cursorstate":
                    request.CursorState = param.Value?.ToString();
                    break;
            }
        }

        return request;
    }
}
