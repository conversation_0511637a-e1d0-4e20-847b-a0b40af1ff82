using Vault.MCP.Shared.DTOs;
using Vault.MCP.Shared.Interfaces;
using Vault.MCP.Shared.Models;

namespace Vault.MCP.Server.Services;

public class McpServerService : IMcpServerService
{
    private readonly IToolService _toolService;
    private readonly IVaultService _vaultService;
    private readonly ILogger<McpServerService> _logger;

    public McpServerService(IToolService toolService, IVaultService vaultService, ILogger<McpServerService> logger)
    {
        _toolService = toolService;
        _vaultService = vaultService;
        _logger = logger;
    }

    public async Task<List<Tool>> GetToolsAsync()
    {
        try
        {
            _logger.LogInformation("Retrieving available tools");
            return await _toolService.GetAvailableToolsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tools");
            throw;
        }
    }

    public async Task<ToolResponseDto> UseToolAsync(ToolRequestDto request)
    {
        try
        {
            _logger.LogInformation("Executing tool: {ToolName}", request.Tool);
            
            // Validate tool exists
            if (!await _toolService.IsToolAvailableAsync(request.Tool))
            {
                return new ToolResponseDto
                {
                    Success = false,
                    Error = $"Tool '{request.Tool}' is not available",
                    Timestamp = DateTime.UtcNow
                };
            }

            return await _toolService.ExecuteToolAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing tool {ToolName}", request.Tool);
            return new ToolResponseDto
            {
                Success = false,
                Error = $"Tool execution failed: {ex.Message}",
                Timestamp = DateTime.UtcNow
            };
        }
    }

    public async Task<bool> HealthCheckAsync()
    {
        try
        {
            _logger.LogDebug("Performing health check");
            
            // Check if tools are available
            var tools = await _toolService.GetAvailableToolsAsync();
            if (!tools.Any())
            {
                _logger.LogWarning("No tools available during health check");
                return false;
            }

            // Check if vault service is responsive (optional)
            // This could be made configurable
            try
            {
                await _vaultService.IsVaultAvailableAsync("117");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Vault service not available during health check, but continuing");
                // Don't fail health check if vault is down, as tools like getTime still work
            }

            _logger.LogDebug("Health check passed");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed");
            return false;
        }
    }
}
