using Microsoft.Extensions.Options;
using System.Text;
using Vault.MCP.Shared.DTOs;
using Vault.MCP.Shared.Extensions;
using Vault.MCP.Shared.Interfaces;
using Vault.MCP.Shared.Models;

namespace Vault.MCP.Server.Services;

public class VaultService : IVaultService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<VaultService> _logger;
    private readonly VaultApiConfiguration _config;

    public VaultService(IHttpClientFactory httpClientFactory, ILogger<VaultService> logger, IOptions<VaultApiConfiguration> config)
    {
        _httpClient = httpClientFactory.CreateClient("vault-api");
        _logger = logger;
        _config = config.Value;
    }

    public async Task<VaultFilesResponse> GetVaultFilesAsync(VaultFilesRequestDto request)
    {
        try
        {
            var vaultId = request.VaultId ?? _config.DefaultVaultId;
            var url = BuildVaultFilesUrl(vaultId, request);

            _logger.LogInformation("Requesting vault files from: {Url}", url);

            var response = await _httpClient.GetStringWithTimeoutAsync(url, TimeSpan.FromSeconds(_config.TimeoutSeconds));
            
            // Parse the mock response or real Vault API response
            var vaultResponse = ParseVaultResponse(response);
            
            _logger.LogInformation("Successfully retrieved {FileCount} vault files", vaultResponse.Files.Count);
            return vaultResponse;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error while retrieving vault files");
            throw new InvalidOperationException($"Failed to connect to Vault API: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout while retrieving vault files");
            throw new TimeoutException($"Vault API request timed out after {_config.TimeoutSeconds} seconds", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error while retrieving vault files");
            throw;
        }
    }

    public async Task<bool> IsVaultAvailableAsync(string vaultId)
    {
        try
        {
            var url = $"/AutodeskDM/Services/api/vault/v2/vaults/{vaultId}";
            await _httpClient.GetStringWithTimeoutAsync(url, TimeSpan.FromSeconds(10));
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Vault {VaultId} is not available", vaultId);
            return false;
        }
    }

    public async Task<object> GetVaultInfoAsync(string vaultId)
    {
        try
        {
            var url = $"/AutodeskDM/Services/api/vault/v2/vaults/{vaultId}";
            var response = await _httpClient.GetStringWithTimeoutAsync(url, TimeSpan.FromSeconds(_config.TimeoutSeconds));
            
            return new
            {
                vaultId,
                status = "available",
                timestamp = DateTime.UtcNow,
                response = response
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vault info for {VaultId}", vaultId);
            throw new InvalidOperationException($"Failed to get vault info: {ex.Message}", ex);
        }
    }

    private string BuildVaultFilesUrl(string vaultId, VaultFilesRequestDto request)
    {
        var url = new StringBuilder($"/AutodeskDM/Services/api/vault/v2/vaults/{vaultId}/file-versions");
        var queryParams = new List<string>();

        // Add query parameters
        if (!string.IsNullOrEmpty(request.FilterState))
            queryParams.Add($"filter[State]={Uri.EscapeDataString(request.FilterState)}");

        if (request.OptionLatestOnly.HasValue)
            queryParams.Add($"option[latestOnly]={request.OptionLatestOnly.Value.ToString().ToLower()}");

        if (request.OptionReleasedFilesOnly.HasValue)
            queryParams.Add($"option[releasedFilesOnly]={request.OptionReleasedFilesOnly.Value.ToString().ToLower()}");

        if (request.OptionExtendedModels.HasValue)
            queryParams.Add($"option[extendedModels]={request.OptionExtendedModels.Value.ToString().ToLower()}");

        if (!string.IsNullOrEmpty(request.OptionPropDefIds))
            queryParams.Add($"option[propDefIds]={Uri.EscapeDataString(request.OptionPropDefIds)}");

        if (!string.IsNullOrEmpty(request.Sort))
            queryParams.Add($"sort={Uri.EscapeDataString(request.Sort)}");

        if (request.Limit.HasValue)
            queryParams.Add($"limit={request.Limit.Value}");

        if (!string.IsNullOrEmpty(request.CursorState))
            queryParams.Add($"cursorState={Uri.EscapeDataString(request.CursorState)}");

        if (queryParams.Any())
            url.Append("?").Append(string.Join("&", queryParams));

        return url.ToString();
    }

    private VaultFilesResponse ParseVaultResponse(string response)
    {
        try
        {
            // For now, create a mock response since we're working with a mock API
            // In a real implementation, this would parse the actual Vault API response
            var mockFiles = new List<VaultFile>
            {
                new VaultFile
                {
                    Id = 1,
                    Name = "Drawing1.dwg",
                    Path = "/Designs/Mechanical/Drawing1.dwg",
                    Revision = "A",
                    State = "Released",
                    Size = 1024000,
                    CreatedDate = DateTime.UtcNow.AddDays(-30),
                    ModifiedDate = DateTime.UtcNow.AddDays(-5),
                    CreatedBy = "john.doe",
                    ModifiedBy = "jane.smith",
                    Properties = new Dictionary<string, object>
                    {
                        { "Title", "Main Assembly Drawing" },
                        { "Project", "Project Alpha" },
                        { "Material", "Steel" }
                    }
                },
                new VaultFile
                {
                    Id = 2,
                    Name = "Model1.ipt",
                    Path = "/Designs/Mechanical/Model1.ipt",
                    Revision = "B",
                    State = "Work in Progress",
                    Size = 2048000,
                    CreatedDate = DateTime.UtcNow.AddDays(-20),
                    ModifiedDate = DateTime.UtcNow.AddDays(-2),
                    CreatedBy = "jane.smith",
                    ModifiedBy = "jane.smith",
                    Properties = new Dictionary<string, object>
                    {
                        { "Title", "Main Component Model" },
                        { "Project", "Project Alpha" },
                        { "Material", "Aluminum" }
                    }
                }
            };

            return new VaultFilesResponse
            {
                Files = mockFiles,
                TotalCount = mockFiles.Count,
                HasMore = false,
                CursorState = null
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing vault response");
            throw new InvalidOperationException("Failed to parse vault response", ex);
        }
    }
}
