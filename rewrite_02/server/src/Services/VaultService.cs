using Microsoft.Extensions.Options;
using System.Text;
using Vault.MCP.Shared.DTOs;
using Vault.MCP.Shared.Extensions;
using Vault.MCP.Shared.Interfaces;
using Vault.MCP.Shared.Models;

namespace Vault.MCP.Server.Services;

public class VaultService : IVaultService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<VaultService> _logger;
    private readonly VaultApiConfiguration _config;

    public VaultService(IHttpClientFactory httpClientFactory, ILogger<VaultService> logger, IOptions<VaultApiConfiguration> config)
    {
        _httpClient = httpClientFactory.CreateClient("vault-api");
        _logger = logger;
        _config = config.Value;
    }

    public async Task<VaultFilesResponse> GetVaultFilesAsync(VaultFilesRequestDto request)
    {
        try
        {
            var vaultId = request.VaultId ?? _config.DefaultVaultId;
            var url = BuildVaultFilesUrl(vaultId, request);

            _logger.LogInformation("Requesting vault files from: {Url}", url);

            var response = await _httpClient.GetStringWithTimeoutAsync(url, TimeSpan.FromSeconds(_config.TimeoutSeconds));
            
            // Parse the mock response or real Vault API response
            var vaultResponse = ParseVaultResponse(response);
            
            _logger.LogInformation("Successfully retrieved {FileCount} vault files", vaultResponse.Files.Count);
            return vaultResponse;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error while retrieving vault files");
            throw new InvalidOperationException($"Failed to connect to Vault API: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout while retrieving vault files");
            throw new TimeoutException($"Vault API request timed out after {_config.TimeoutSeconds} seconds", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error while retrieving vault files");
            throw;
        }
    }

    public async Task<bool> IsVaultAvailableAsync(string vaultId)
    {
        try
        {
            var url = $"/AutodeskDM/Services/api/vault/v2/vaults/{vaultId}";
            await _httpClient.GetStringWithTimeoutAsync(url, TimeSpan.FromSeconds(10));
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Vault {VaultId} is not available", vaultId);
            return false;
        }
    }

    public async Task<object> GetVaultInfoAsync(string vaultId)
    {
        try
        {
            var url = $"/AutodeskDM/Services/api/vault/v2/vaults/{vaultId}";
            var response = await _httpClient.GetStringWithTimeoutAsync(url, TimeSpan.FromSeconds(_config.TimeoutSeconds));
            
            return new
            {
                vaultId,
                status = "available",
                timestamp = DateTime.UtcNow,
                response = response
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vault info for {VaultId}", vaultId);
            throw new InvalidOperationException($"Failed to get vault info: {ex.Message}", ex);
        }
    }

    private string BuildVaultFilesUrl(string vaultId, VaultFilesRequestDto request)
    {
        var url = new StringBuilder($"/AutodeskDM/Services/api/vault/v2/vaults/{vaultId}/file-versions");
        var queryParams = new List<string>();

        // Add query parameters
        if (!string.IsNullOrEmpty(request.FilterState))
            queryParams.Add($"filter[State]={Uri.EscapeDataString(request.FilterState)}");

        if (request.OptionLatestOnly.HasValue)
            queryParams.Add($"option[latestOnly]={request.OptionLatestOnly.Value.ToString().ToLower()}");

        if (request.OptionReleasedFilesOnly.HasValue)
            queryParams.Add($"option[releasedFilesOnly]={request.OptionReleasedFilesOnly.Value.ToString().ToLower()}");

        if (request.OptionExtendedModels.HasValue)
            queryParams.Add($"option[extendedModels]={request.OptionExtendedModels.Value.ToString().ToLower()}");

        if (!string.IsNullOrEmpty(request.OptionPropDefIds))
            queryParams.Add($"option[propDefIds]={Uri.EscapeDataString(request.OptionPropDefIds)}");

        if (!string.IsNullOrEmpty(request.Sort))
            queryParams.Add($"sort={Uri.EscapeDataString(request.Sort)}");

        if (request.Limit.HasValue)
            queryParams.Add($"limit={request.Limit.Value}");

        if (!string.IsNullOrEmpty(request.CursorState))
            queryParams.Add($"cursorState={Uri.EscapeDataString(request.CursorState)}");

        if (queryParams.Any())
            url.Append("?").Append(string.Join("&", queryParams));

        return url.ToString();
    }

    private VaultFilesResponse ParseVaultResponse(string response)
    {
        try
        {
            _logger.LogDebug("Parsing vault response: {Response}", response);

            // Parse the actual mock API response
            using var document = System.Text.Json.JsonDocument.Parse(response);
            var root = document.RootElement;

            var files = new List<VaultFile>();

            if (root.TryGetProperty("results", out var resultsElement))
            {
                foreach (var item in resultsElement.EnumerateArray())
                {
                    var file = new VaultFile();

                    if (item.TryGetProperty("id", out var idElement))
                        file.Id = long.TryParse(idElement.GetString(), out var id) ? id : 0;

                    if (item.TryGetProperty("name", out var nameElement))
                        file.Name = nameElement.GetString() ?? "";

                    if (item.TryGetProperty("state", out var stateElement))
                        file.State = stateElement.GetString() ?? "";

                    if (item.TryGetProperty("revision", out var revisionElement))
                        file.Revision = revisionElement.GetString() ?? "";

                    if (item.TryGetProperty("size", out var sizeElement))
                        file.Size = sizeElement.GetInt64();

                    if (item.TryGetProperty("lastModifiedDate", out var modifiedElement))
                    {
                        if (DateTime.TryParse(modifiedElement.GetString(), out var modifiedDate))
                            file.ModifiedDate = modifiedDate;
                    }

                    if (item.TryGetProperty("createDate", out var createElement))
                    {
                        if (DateTime.TryParse(createElement.GetString(), out var createDate))
                            file.CreatedDate = createDate;
                    }

                    if (item.TryGetProperty("createUserName", out var createUserElement))
                        file.CreatedBy = createUserElement.GetString() ?? "";

                    if (item.TryGetProperty("checkoutUserName", out var checkoutUserElement))
                        file.ModifiedBy = checkoutUserElement.GetString() ?? file.CreatedBy;

                    // Build path from parent folder info if available
                    if (root.TryGetProperty("included", out var includedElement) &&
                        includedElement.TryGetProperty("folder", out var folderElement) &&
                        item.TryGetProperty("parentFolderId", out var parentIdElement))
                    {
                        var parentId = parentIdElement.GetString();
                        if (folderElement.TryGetProperty(parentId, out var parentFolder) &&
                            parentFolder.TryGetProperty("fullName", out var fullNameElement))
                        {
                            file.Path = $"{fullNameElement.GetString()}/{file.Name}";
                        }
                    }

                    if (string.IsNullOrEmpty(file.Path))
                        file.Path = $"/{file.Name}";

                    // Add some properties based on available data
                    file.Properties = new Dictionary<string, object>();

                    if (item.TryGetProperty("category", out var categoryElement))
                        file.Properties["Category"] = categoryElement.GetString() ?? "";

                    if (item.TryGetProperty("version", out var versionElement))
                        file.Properties["Version"] = versionElement.GetInt32();

                    if (item.TryGetProperty("isCheckedOut", out var checkedOutElement))
                        file.Properties["IsCheckedOut"] = checkedOutElement.GetBoolean();

                    files.Add(file);
                }
            }

            // Get pagination info
            var totalCount = files.Count;
            var hasMore = false;

            if (root.TryGetProperty("pagination", out var paginationElement))
            {
                if (paginationElement.TryGetProperty("totalResults", out var totalElement))
                    totalCount = totalElement.GetInt32();

                if (paginationElement.TryGetProperty("nextUrl", out var nextUrlElement))
                    hasMore = !string.IsNullOrEmpty(nextUrlElement.GetString());
            }

            var result = new VaultFilesResponse
            {
                Files = files,
                TotalCount = totalCount,
                HasMore = hasMore,
                CursorState = null
            };

            _logger.LogInformation("Parsed {FileCount} files from vault response", files.Count);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing vault response: {Response}", response);
            throw new InvalidOperationException("Failed to parse vault response", ex);
        }
    }
}
