{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.AspNetCore.Hosting": "Information", "Vault.MCP.Server": "Debug"}}, "AllowedHosts": "*", "VaultApi": {"BaseUrl": "http://localhost:4000", "DefaultVaultId": "117", "TimeoutSeconds": 60, "EnableRetry": true, "MaxRetryAttempts": 3}, "McpServer": {"BaseUrl": "http://localhost:5001", "TimeoutSeconds": 30, "EnableHealthCheck": true, "HealthCheckIntervalSeconds": 60}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://localhost:5001"}}}}