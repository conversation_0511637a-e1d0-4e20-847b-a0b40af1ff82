using System.Text.Json.Serialization;

namespace Vault.MCP.Shared.DTOs;

public class ToolRequestDto
{
    [JsonPropertyName("tool")]
    public string Tool { get; set; } = string.Empty;

    [JsonPropertyName("params")]
    public Dictionary<string, object>? Params { get; set; }
}

public class ToolResponseDto
{
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    [JsonPropertyName("data")]
    public object? Data { get; set; }

    [JsonPropertyName("error")]
    public string? Error { get; set; }

    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class VaultFilesRequestDto
{
    [JsonPropertyName("vaultId")]
    public string? VaultId { get; set; }

    [JsonPropertyName("filter_State")]
    public string? FilterState { get; set; }

    [JsonPropertyName("option_latestOnly")]
    public bool? OptionLatestOnly { get; set; }

    [JsonPropertyName("option_releasedFilesOnly")]
    public bool? OptionReleasedFilesOnly { get; set; }

    [JsonPropertyName("option_extendedModels")]
    public bool? OptionExtendedModels { get; set; }

    [JsonPropertyName("option_propDefIds")]
    public string? OptionPropDefIds { get; set; }

    [JsonPropertyName("sort")]
    public string? Sort { get; set; }

    [JsonPropertyName("limit")]
    public int? Limit { get; set; }

    [JsonPropertyName("cursorState")]
    public string? CursorState { get; set; }
}
