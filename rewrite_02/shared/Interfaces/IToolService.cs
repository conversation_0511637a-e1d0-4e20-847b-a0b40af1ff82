using Vault.MCP.Shared.DTOs;
using Vault.MCP.Shared.Models;

namespace Vault.MCP.Shared.Interfaces;

public interface IToolService
{
    Task<List<Tool>> GetAvailableToolsAsync();
    Task<ToolResponseDto> ExecuteToolAsync(ToolRequestDto request);
    Task<bool> IsToolAvailableAsync(string toolName);
}

public interface IVaultService
{
    Task<VaultFilesResponse> GetVaultFilesAsync(VaultFilesRequestDto request);
    Task<bool> IsVaultAvailableAsync(string vaultId);
    Task<object> GetVaultInfoAsync(string vaultId);
}

public interface IAiService
{
    Task<string> GenerateResponseAsync(string prompt, string? context = null);
    Task<List<PlanStepDto>> GeneratePlanAsync(string userMessage, List<Tool> availableTools);
    Task<string> GenerateFinalAnswerAsync(string userMessage, List<object> toolResults);
}

public interface IMcpServerService
{
    Task<List<Tool>> GetToolsAsync();
    Task<ToolResponseDto> UseToolAsync(ToolRequestDto request);
    Task<bool> HealthCheckAsync();
}

public interface IMcpClientService
{
    Task<ChatResponseDto> ProcessChatRequestAsync(ChatRequestDto request);
    Task<List<Tool>> GetAvailableToolsAsync();
    Task<ToolResponseDto> ExecuteToolAsync(ToolRequestDto request);
}
