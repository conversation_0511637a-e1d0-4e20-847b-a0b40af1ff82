namespace Vault.MCP.Shared.Models;

public class VaultApiConfiguration
{
    public string BaseUrl { get; set; } = "http://localhost:4000";
    public string DefaultVaultId { get; set; } = "117";
    public int TimeoutSeconds { get; set; } = 60;
    public bool EnableRetry { get; set; } = true;
    public int MaxRetryAttempts { get; set; } = 3;
}

public class AiApiConfiguration
{
    public string BaseUrl { get; set; } = "https://api.epicai.fun/v1";
    public string ApiKey { get; set; } = "ecs-00";
    public string DefaultModel { get; set; } = "gpt-3.5-turbo";
    public double Temperature { get; set; } = 0.7;
    public int MaxTokens { get; set; } = 1000;
    public int TimeoutSeconds { get; set; } = 120;
}

public class McpServerConfiguration
{
    public string BaseUrl { get; set; } = "http://localhost:5001";
    public int TimeoutSeconds { get; set; } = 30;
    public bool EnableHealthCheck { get; set; } = true;
    public int HealthCheckIntervalSeconds { get; set; } = 60;
}

public class McpClientConfiguration
{
    public string BaseUrl { get; set; } = "http://localhost:5098";
    public int TimeoutSeconds { get; set; } = 30;
    public bool EnableCors { get; set; } = true;
    public string[] AllowedOrigins { get; set; } = { "*" };
}

public class ApplicationConfiguration
{
    public VaultApiConfiguration VaultApi { get; set; } = new();
    public AiApiConfiguration AiApi { get; set; } = new();
    public McpServerConfiguration McpServer { get; set; } = new();
    public McpClientConfiguration McpClient { get; set; } = new();
    public LoggingConfiguration Logging { get; set; } = new();
}

public class LoggingConfiguration
{
    public string LogLevel { get; set; } = "Information";
    public bool EnableConsoleLogging { get; set; } = true;
    public bool EnableFileLogging { get; set; } = false;
    public string LogFilePath { get; set; } = "logs/app.log";
}
