using System.Text.Json.Serialization;

namespace Vault.MCP.Shared.Models;

public class Tool
{
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    [JsonPropertyName("parameters")]
    public object Parameters { get; set; } = new { };
}

public class ToolParameter
{
    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("required")]
    public bool Required { get; set; }

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    [JsonPropertyName("default")]
    public object? Default { get; set; }
}

public class ToolParameters
{
    [JsonPropertyName("vaultId")]
    public ToolParameter? VaultId { get; set; }

    [JsonPropertyName("filter_State")]
    public ToolParameter? FilterState { get; set; }

    [JsonPropertyName("option_latestOnly")]
    public ToolParameter? OptionLatestOnly { get; set; }

    [JsonPropertyName("option_releasedFilesOnly")]
    public ToolParameter? OptionReleasedFilesOnly { get; set; }

    [JsonPropertyName("option_extendedModels")]
    public ToolParameter? OptionExtendedModels { get; set; }

    [JsonPropertyName("option_propDefIds")]
    public ToolParameter? OptionPropDefIds { get; set; }

    [JsonPropertyName("sort")]
    public ToolParameter? Sort { get; set; }

    [JsonPropertyName("limit")]
    public ToolParameter? Limit { get; set; }

    [JsonPropertyName("cursorState")]
    public ToolParameter? CursorState { get; set; }
}
