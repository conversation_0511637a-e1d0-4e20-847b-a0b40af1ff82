using System.Text;
using System.Text.Json;

namespace Vault.MCP.Shared.Extensions;

public static class HttpClientExtensions
{
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false
    };

    public static async Task<T?> GetFromJsonAsync<T>(this HttpClient httpClient, string requestUri)
    {
        var response = await httpClient.GetAsync(requestUri);
        response.EnsureSuccessStatusCode();
        
        var content = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<T>(content, JsonOptions);
    }

    public static async Task<HttpResponseMessage> PostAsJsonAsync<T>(this HttpClient httpClient, string requestUri, T value)
    {
        var json = JsonSerializer.Serialize(value, JsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        return await httpClient.PostAsync(requestUri, content);
    }

    public static async Task<TResponse?> PostAsJsonAsync<TRequest, TResponse>(this HttpClient httpClient, string requestUri, TRequest value)
    {
        var response = await httpClient.PostAsJsonAsync(requestUri, value);
        response.EnsureSuccessStatusCode();
        
        var content = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<TResponse>(content, JsonOptions);
    }

    public static async Task<string> GetStringWithTimeoutAsync(this HttpClient httpClient, string requestUri, TimeSpan timeout)
    {
        using var cts = new CancellationTokenSource(timeout);
        var response = await httpClient.GetAsync(requestUri, cts.Token);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadAsStringAsync();
    }
}
