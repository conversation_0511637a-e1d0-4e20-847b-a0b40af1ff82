using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Vault.MCP.Shared.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddSharedServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Add HTTP client with default configuration
        services.AddHttpClient("default", client =>
        {
            client.Timeout = TimeSpan.FromSeconds(30);
            client.DefaultRequestHeaders.Add("User-Agent", "Vault-MCP-Client/1.0");
        });

        // Add named HTTP clients for specific services
        services.AddHttpClient("vault-api", client =>
        {
            var baseUrl = configuration.GetValue<string>("VaultApi:BaseUrl") ?? "http://localhost:4000";
            client.BaseAddress = new Uri(baseUrl);
            client.Timeout = TimeSpan.FromSeconds(60);
        });

        services.AddHttpClient("ai-api", client =>
        {
            var baseUrl = configuration.GetValue<string>("AiApi:BaseUrl") ?? "https://api.epicai.fun/v1";
            client.BaseAddress = new Uri(baseUrl);
            client.Timeout = TimeSpan.FromSeconds(120);
            
            var apiKey = configuration.GetValue<string>("AiApi:ApiKey");
            if (!string.IsNullOrEmpty(apiKey))
            {
                client.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", apiKey);
            }
        });

        services.AddHttpClient("mcp-server", client =>
        {
            var baseUrl = configuration.GetValue<string>("McpServer:BaseUrl") ?? "http://localhost:5001";
            client.BaseAddress = new Uri(baseUrl);
            client.Timeout = TimeSpan.FromSeconds(30);
        });

        return services;
    }

    public static IServiceCollection AddLoggingConfiguration(this IServiceCollection services)
    {
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.AddDebug();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        return services;
    }
}
