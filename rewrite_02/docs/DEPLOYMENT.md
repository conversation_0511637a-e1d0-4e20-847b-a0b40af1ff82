# Deployment Guide

## Overview

This guide covers deploying the Vault MCP AI Rewrite 02 system to various environments, from development to production.

## Prerequisites

- .NET 8 Runtime or SDK
- Web server (IIS, Nginx, Apache) for production
- Reverse proxy for load balancing (optional)
- SSL certificate for HTTPS (production)

## Environment Configuration

### Development Environment

**Requirements**:
- .NET 8 SDK
- Visual Studio, VS Code, or Rider
- Node.js (for mock Vault API)

**Setup**:
```bash
# Clone repository
git clone <repository-url>
cd rewrite_02

# Restore dependencies
cd server && dotnet restore
cd ../client && dotnet restore
cd ../shared && dotnet restore

# Run in development mode
cd server && dotnet run --environment Development
cd client && dotnet run --environment Development
```

### Staging Environment

**Configuration**:
Create `appsettings.Staging.json` files:

**Server** (`server/appsettings.Staging.json`):
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "VaultApi": {
    "BaseUrl": "https://staging-vault-api.company.com",
    "DefaultVaultId": "117",
    "TimeoutSeconds": 60
  },
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://0.0.0.0:5001"
      }
    }
  }
}
```

**Client** (`client/appsettings.Staging.json`):
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AiApi": {
    "BaseUrl": "https://api.openai.com/v1",
    "ApiKey": "${AI_API_KEY}",
    "DefaultModel": "gpt-3.5-turbo"
  },
  "McpServer": {
    "BaseUrl": "https://staging-mcp-server.company.com"
  },
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://0.0.0.0:5098"
      }
    }
  }
}
```

### Production Environment

**Configuration**:
Create `appsettings.Production.json` files with production settings.

**Security Considerations**:
- Use environment variables for sensitive data
- Enable HTTPS
- Configure proper CORS policies
- Set up monitoring and logging

## Deployment Methods

### 1. Self-Contained Deployment

**Build**:
```bash
# Server
cd server
dotnet publish -c Release -r linux-x64 --self-contained true -o ./publish

# Client
cd client
dotnet publish -c Release -r linux-x64 --self-contained true -o ./publish
```

**Deploy**:
```bash
# Copy files to server
scp -r ./publish user@server:/opt/vault-mcp-server/
scp -r ./publish user@server:/opt/vault-mcp-client/

# Set permissions
chmod +x /opt/vault-mcp-server/Vault.MCP.Server
chmod +x /opt/vault-mcp-client/Vault.MCP.Client
```

### 2. Framework-Dependent Deployment

**Build**:
```bash
# Server
cd server
dotnet publish -c Release -o ./publish

# Client
cd client
dotnet publish -c Release -o ./publish
```

**Deploy**:
Requires .NET 8 runtime on target server.

### 3. Docker Deployment

**Server Dockerfile** (`server/Dockerfile`):
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 5001

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["server/Vault.MCP.Server.csproj", "server/"]
COPY ["shared/Vault.MCP.Shared.csproj", "shared/"]
RUN dotnet restore "server/Vault.MCP.Server.csproj"
COPY . .
WORKDIR "/src/server"
RUN dotnet build "Vault.MCP.Server.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Vault.MCP.Server.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Vault.MCP.Server.dll"]
```

**Client Dockerfile** (`client/Dockerfile`):
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 5098

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["client/Vault.MCP.Client.csproj", "client/"]
COPY ["shared/Vault.MCP.Shared.csproj", "shared/"]
RUN dotnet restore "client/Vault.MCP.Client.csproj"
COPY . .
WORKDIR "/src/client"
RUN dotnet build "Vault.MCP.Client.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Vault.MCP.Client.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Vault.MCP.Client.dll"]
```

**Docker Compose** (`docker-compose.yml`):
```yaml
version: '3.8'

services:
  vault-mcp-server:
    build:
      context: .
      dockerfile: server/Dockerfile
    ports:
      - "5001:5001"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - VaultApi__BaseUrl=http://vault-mock-api:4000
    depends_on:
      - vault-mock-api
    networks:
      - vault-mcp-network

  vault-mcp-client:
    build:
      context: .
      dockerfile: client/Dockerfile
    ports:
      - "5098:5098"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - McpServer__BaseUrl=http://vault-mcp-server:5001
      - AiApi__ApiKey=${AI_API_KEY}
    depends_on:
      - vault-mcp-server
    networks:
      - vault-mcp-network

  vault-mock-api:
    image: node:18-alpine
    working_dir: /app
    volumes:
      - ../vault-mock-api:/app
    command: node index.js
    ports:
      - "4000:4000"
    networks:
      - vault-mcp-network

networks:
  vault-mcp-network:
    driver: bridge
```

**Build and Run**:
```bash
# Build images
docker-compose build

# Run services
docker-compose up -d

# View logs
docker-compose logs -f
```

## Web Server Configuration

### Nginx Configuration

**Site Configuration** (`/etc/nginx/sites-available/vault-mcp`):
```nginx
upstream vault-mcp-client {
    server 127.0.0.1:5098;
}

upstream vault-mcp-server {
    server 127.0.0.1:5001;
}

server {
    listen 80;
    server_name vault-mcp.company.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name vault-mcp.company.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    # Client (main application)
    location / {
        proxy_pass http://vault-mcp-client;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Server API
    location /api/server/ {
        rewrite ^/api/server/(.*) /$1 break;
        proxy_pass http://vault-mcp-server;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### IIS Configuration

**web.config** (for client):
```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" 
                  arguments=".\Vault.MCP.Client.dll" 
                  stdoutLogEnabled="false" 
                  stdoutLogFile=".\logs\stdout" 
                  hostingModel="inprocess" />
    </system.webServer>
  </location>
</configuration>
```

## Environment Variables

### Server Environment Variables
```bash
# Vault API Configuration
VAULTAPI__BASEURL=https://vault-api.company.com
VAULTAPI__DEFAULTVAULTID=117
VAULTAPI__TIMEOUTSECONDS=60

# Logging
LOGGING__LOGLEVEL__DEFAULT=Information

# Kestrel
ASPNETCORE_URLS=http://0.0.0.0:5001
```

### Client Environment Variables
```bash
# AI API Configuration
AIAPI__BASEURL=https://api.openai.com/v1
AIAPI__APIKEY=your-api-key-here
AIAPI__DEFAULTMODEL=gpt-3.5-turbo

# MCP Server Configuration
MCPSERVER__BASEURL=https://mcp-server.company.com

# Kestrel
ASPNETCORE_URLS=http://0.0.0.0:5098
```

## Monitoring and Health Checks

### Health Check Endpoints
- Server: `https://vault-mcp.company.com/api/server/health`
- Client: `https://vault-mcp.company.com/health`

### Monitoring Setup

**Prometheus Configuration** (`prometheus.yml`):
```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'vault-mcp-server'
    static_configs:
      - targets: ['vault-mcp-server:5001']
    metrics_path: '/metrics'
    
  - job_name: 'vault-mcp-client'
    static_configs:
      - targets: ['vault-mcp-client:5098']
    metrics_path: '/metrics'
```

### Log Management

**Serilog Configuration** (add to `appsettings.Production.json`):
```json
{
  "Serilog": {
    "Using": ["Serilog.Sinks.File", "Serilog.Sinks.Console"],
    "MinimumLevel": "Information",
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "/var/log/vault-mcp/app-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30
        }
      },
      {
        "Name": "Console"
      }
    ]
  }
}
```

## SSL/TLS Configuration

### Let's Encrypt with Certbot
```bash
# Install certbot
sudo apt-get install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d vault-mcp.company.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Custom Certificate
```bash
# Generate self-signed certificate (development only)
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# Configure in appsettings.json
{
  "Kestrel": {
    "Endpoints": {
      "Https": {
        "Url": "https://0.0.0.0:5001",
        "Certificate": {
          "Path": "/path/to/cert.pem",
          "KeyPath": "/path/to/key.pem"
        }
      }
    }
  }
}
```

## Backup and Recovery

### Application Backup
```bash
# Backup application files
tar -czf vault-mcp-backup-$(date +%Y%m%d).tar.gz /opt/vault-mcp-*/

# Backup configuration
cp /opt/vault-mcp-*/appsettings.Production.json /backup/config/
```

### Database Backup (Future)
When database is implemented:
```bash
# PostgreSQL example
pg_dump vault_mcp_db > vault_mcp_backup_$(date +%Y%m%d).sql
```

## Troubleshooting

### Common Issues

1. **Port Binding Issues**
   ```bash
   # Check port usage
   netstat -tulpn | grep :5001
   
   # Kill process if needed
   sudo kill -9 <PID>
   ```

2. **Permission Issues**
   ```bash
   # Set correct permissions
   sudo chown -R www-data:www-data /opt/vault-mcp-*
   sudo chmod +x /opt/vault-mcp-*/Vault.MCP.*
   ```

3. **SSL Certificate Issues**
   ```bash
   # Check certificate validity
   openssl x509 -in certificate.crt -text -noout
   
   # Test SSL connection
   openssl s_client -connect vault-mcp.company.com:443
   ```

### Log Analysis
```bash
# View application logs
tail -f /var/log/vault-mcp/app-*.log

# View system logs
journalctl -u vault-mcp-server -f
journalctl -u vault-mcp-client -f
```

## Performance Optimization

### Application Settings
```json
{
  "Kestrel": {
    "Limits": {
      "MaxConcurrentConnections": 100,
      "MaxConcurrentUpgradedConnections": 100,
      "MaxRequestBodySize": 10485760
    }
  }
}
```

### Reverse Proxy Caching
```nginx
# Add to nginx configuration
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## Security Hardening

### Application Security
- Enable HTTPS only
- Configure CORS properly
- Implement rate limiting
- Use secure headers

### Server Security
- Keep OS updated
- Configure firewall
- Use fail2ban for intrusion prevention
- Regular security audits
