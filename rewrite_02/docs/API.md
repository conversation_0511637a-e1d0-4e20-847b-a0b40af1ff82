# API Documentation

## Overview

The Vault MCP AI system provides two main APIs:
- **MCP Server API**: Tool discovery and execution
- **MCP Client API**: AI-powered chat interface

Both APIs follow REST principles and use JSON for data exchange.

## Base URLs

- **MCP Server**: `http://localhost:5001`
- **MCP Client**: `http://localhost:5098`

## Authentication

Currently, no authentication is required. Future versions may include API key authentication.

## Common Response Format

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error description",
  "timestamp": "2024-01-01T12:00:00Z",
  "traceId": "unique-trace-id"
}
```

## MCP Server API

### Get Available Tools

**Endpoint**: `GET /tools`

**Description**: Retrieves all available tools that can be executed.

**Response**:
```json
[
  {
    "name": "getTime",
    "description": "Returns the current server time in ISO 8601 format",
    "parameters": {}
  },
  {
    "name": "getVaultFiles",
    "description": "Retrieves files from Autodesk Vault with filtering and sorting options",
    "parameters": {
      "vaultId": {
        "type": "string",
        "required": false,
        "description": "Vault ID (default: 117)",
        "default": "117"
      },
      "filter_State": {
        "type": "string",
        "required": false,
        "description": "Filter by file state"
      },
      "option_latestOnly": {
        "type": "boolean",
        "required": false,
        "description": "Only latest version (default: true)",
        "default": true
      },
      "option_releasedFilesOnly": {
        "type": "boolean",
        "required": false,
        "description": "Only released files (default: false)",
        "default": false
      },
      "sort": {
        "type": "string",
        "required": false,
        "description": "Sort criteria, e.g. 'Revision desc,Name asc'"
      },
      "limit": {
        "type": "number",
        "required": false,
        "description": "Number of results to return (default: 10, max: 100)",
        "default": 10
      }
    }
  }
]
```

### Execute Tool

**Endpoint**: `POST /tool`

**Description**: Executes a specific tool with provided parameters.

**Request Body**:
```json
{
  "tool": "getVaultFiles",
  "params": {
    "vaultId": "117",
    "option_releasedFilesOnly": true,
    "limit": 5
  }
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "id": 1,
        "name": "Drawing1.dwg",
        "path": "/Designs/Mechanical/Drawing1.dwg",
        "revision": "A",
        "state": "Released",
        "size": 1024000,
        "createdDate": "2024-01-01T10:00:00Z",
        "modifiedDate": "2024-01-15T14:30:00Z",
        "createdBy": "john.doe",
        "modifiedBy": "jane.smith",
        "properties": {
          "Title": "Main Assembly Drawing",
          "Project": "Project Alpha",
          "Material": "Steel"
        }
      }
    ],
    "totalCount": 1,
    "hasMore": false,
    "cursorState": null
  },
  "timestamp": "2024-01-20T12:00:00Z"
}
```

### Health Check

**Endpoint**: `GET /health`

**Description**: Returns the health status of the server and its dependencies.

**Response**:
```json
{
  "status": "Healthy",
  "totalDuration": "00:00:00.0234567",
  "entries": {
    "vault-api": {
      "status": "Healthy",
      "description": "Vault API is responsive",
      "duration": "00:00:00.0123456"
    }
  }
}
```

## MCP Client API

### Process Chat Message

**Endpoint**: `POST /api/chat`

**Description**: Processes a user message using AI and available tools.

**Request Body**:
```json
{
  "message": "Show me the latest CAD files",
  "sessionId": "optional-session-id",
  "context": {
    "optional": "context-data"
  }
}
```

**Response**:
```json
{
  "reply": "I found 2 CAD files in your vault. Here are the latest ones:\n\n1. Drawing1.dwg - Revision A (Released)\n2. Model1.ipt - Revision B (Work in Progress)\n\nBoth files are from Project Alpha and were recently modified.",
  "sessionId": "session_1234567890_abc123",
  "toolsUsed": ["getVaultFiles"],
  "success": true,
  "timestamp": "2024-01-20T12:00:00Z"
}
```

### Get Available Tools (Client)

**Endpoint**: `GET /api/chat/tools`

**Description**: Retrieves available tools from the MCP server.

**Response**: Same as MCP Server `/tools` endpoint.

### Execute Tool Directly

**Endpoint**: `POST /api/chat/tool`

**Description**: Executes a tool directly without AI processing.

**Request/Response**: Same as MCP Server `/tool` endpoint.

### Legacy Chat Endpoint

**Endpoint**: `POST /ask`

**Description**: Legacy endpoint for backward compatibility.

**Request Body**:
```json
{
  "Message": "What time is it?"
}
```

**Response**:
```json
{
  "reply": "The current server time is 2024-01-20T12:00:00.000Z (UTC)."
}
```

## Tool Specifications

### getTime Tool

**Purpose**: Returns the current server time.

**Parameters**: None

**Example Request**:
```json
{
  "tool": "getTime",
  "params": {}
}
```

**Example Response**:
```json
{
  "success": true,
  "data": {
    "time": "2024-01-20T12:00:00.000Z",
    "timezone": "UTC"
  },
  "timestamp": "2024-01-20T12:00:00Z"
}
```

### getVaultFiles Tool

**Purpose**: Retrieves files from Autodesk Vault with filtering and sorting.

**Parameters**:
- `vaultId` (string, optional): Target vault ID
- `filter_State` (string, optional): Filter by file state
- `option_latestOnly` (boolean, optional): Return only latest versions
- `option_releasedFilesOnly` (boolean, optional): Return only released files
- `option_extendedModels` (boolean, optional): Include extended model information
- `option_propDefIds` (string, optional): Property definition IDs to include
- `sort` (string, optional): Sort criteria
- `limit` (number, optional): Maximum number of results (1-100)
- `cursorState` (string, optional): Pagination cursor

**Example Request**:
```json
{
  "tool": "getVaultFiles",
  "params": {
    "vaultId": "117",
    "filter_State": "Released",
    "option_latestOnly": true,
    "sort": "ModifiedDate desc",
    "limit": 10
  }
}
```

**Example Response**:
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "id": 1,
        "name": "Assembly.iam",
        "path": "/Designs/Assembly.iam",
        "revision": "C",
        "state": "Released",
        "size": 2048000,
        "createdDate": "2024-01-01T08:00:00Z",
        "modifiedDate": "2024-01-20T10:30:00Z",
        "createdBy": "engineer1",
        "modifiedBy": "engineer2",
        "properties": {
          "Title": "Main Assembly",
          "Project": "Alpha",
          "Material": "Steel",
          "PartNumber": "ASM-001"
        }
      }
    ],
    "totalCount": 1,
    "hasMore": false,
    "cursorState": null
  },
  "timestamp": "2024-01-20T12:00:00Z"
}
```

## Error Codes

| HTTP Status | Error Type | Description |
|-------------|------------|-------------|
| 400 | Bad Request | Invalid request parameters or malformed JSON |
| 401 | Unauthorized | Authentication required (future feature) |
| 404 | Not Found | Endpoint or resource not found |
| 408 | Request Timeout | Request took too long to process |
| 429 | Too Many Requests | Rate limit exceeded (future feature) |
| 500 | Internal Server Error | Unexpected server error |
| 502 | Bad Gateway | External service error |
| 503 | Service Unavailable | Service temporarily unavailable |

## Rate Limiting

Currently, no rate limiting is implemented. Future versions may include:
- Per-IP rate limiting
- Per-session rate limiting
- Tool-specific rate limiting

## Pagination

For endpoints that return large datasets (like `getVaultFiles`), pagination is supported:

**Request Parameters**:
- `limit`: Number of items per page (default: 10, max: 100)
- `cursorState`: Opaque cursor for next page

**Response Fields**:
- `hasMore`: Boolean indicating if more results are available
- `cursorState`: Cursor for the next page (if `hasMore` is true)

## Webhooks (Future Feature)

Future versions may support webhooks for:
- Tool execution completion
- Vault file changes
- System health alerts

## SDK and Client Libraries

Currently, no official SDKs are provided. The APIs can be consumed using standard HTTP clients in any programming language.

Example using curl:

```bash
# Get available tools
curl -X GET http://localhost:5001/tools

# Execute a tool
curl -X POST http://localhost:5001/tool \
  -H "Content-Type: application/json" \
  -d '{"tool":"getTime","params":{}}'

# Send a chat message
curl -X POST http://localhost:5098/ask \
  -H "Content-Type: application/json" \
  -d '{"Message":"What time is it?"}'
```
