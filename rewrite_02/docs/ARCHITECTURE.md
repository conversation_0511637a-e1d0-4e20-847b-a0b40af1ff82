# Architecture Documentation

## Overview

The Vault MCP AI Rewrite 02 follows a modern, layered architecture with clear separation of concerns. The system is built using .NET 8 and follows Domain-Driven Design (DDD) principles.

## System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │   AI Service    │    │  Vault Mock API │
│   (Browser)     │    │  (External)     │    │   (Node.js)     │
└─────────┬───────┘    └─────────────────┘    └─────────┬───────┘
          │                       │                      │
          │ HTTP/JSON             │ HTTP/JSON            │ HTTP/JSON
          │                       │                      │
┌─────────▼───────┐              │              ┌───────▼─────────┐
│   MCP Client    │              │              │   MCP Server    │
│   (.NET 8)      │◄─────────────┼──────────────┤   (.NET 8)      │
└─────────────────┘              │              └─────────────────┘
                                 │
                          ┌──────▼──────┐
                          │   Shared    │
                          │  Libraries  │
                          └─────────────┘
```

## Component Architecture

### 1. Shared Layer
**Purpose**: Common models, DTOs, interfaces, and utilities shared between client and server.

**Components**:
- **Models**: Domain entities (Tool, VaultFile, Configuration)
- **DTOs**: Data transfer objects for API communication
- **Interfaces**: Service contracts and abstractions
- **Extensions**: Utility methods and extension functions

**Key Patterns**:
- Repository pattern interfaces
- Configuration objects
- HTTP client extensions

### 2. MCP Server
**Purpose**: Provides tools and integrates with external services (Vault API).

**Layers**:
```
Controllers (API Layer)
    ↓
Services (Business Logic)
    ↓
External APIs (Data Layer)
```

**Components**:
- **Controllers**: REST API endpoints
- **Services**: Business logic and tool implementations
- **Middleware**: Cross-cutting concerns (logging, error handling)
- **Health Checks**: Service monitoring

**Key Patterns**:
- Dependency Injection
- Middleware pipeline
- Health check pattern
- Configuration pattern

### 3. MCP Client
**Purpose**: Provides AI-powered chat interface and orchestrates tool usage.

**Layers**:
```
Web UI (Presentation)
    ↓
Controllers (API Layer)
    ↓
Services (Business Logic)
    ↓
External Services (Integration)
```

**Components**:
- **Web UI**: Modern responsive interface
- **Controllers**: Chat and tool API endpoints
- **Services**: AI integration and session management
- **Middleware**: Error handling and request logging

**Key Patterns**:
- Session management
- Caching pattern
- Circuit breaker (for external services)
- Command pattern (for tool execution)

## Data Flow

### 1. Chat Request Flow
```
User Input → Web UI → Client Controller → MCP Client Service
    ↓
AI Service (Plan Generation) → Tool Execution → MCP Server
    ↓
Vault API → Response Processing → AI Service (Final Answer)
    ↓
Client Response → Web UI → User
```

### 2. Tool Execution Flow
```
Tool Request → MCP Server Controller → Tool Service
    ↓
Vault Service → External Vault API → Response Processing
    ↓
Tool Response → Client
```

## Design Patterns

### 1. Dependency Injection
- All services are registered in the DI container
- Interfaces are used for loose coupling
- Configuration objects are injected via IOptions<T>

### 2. Repository Pattern
- Abstracted data access through interfaces
- Consistent error handling
- Testable service layer

### 3. Middleware Pattern
- Cross-cutting concerns handled in middleware
- Request/response logging
- Global error handling

### 4. Health Check Pattern
- Proactive monitoring of dependencies
- Graceful degradation when services are unavailable
- Standardized health endpoints

### 5. Configuration Pattern
- Strongly-typed configuration objects
- Environment-specific settings
- Validation and defaults

## Error Handling Strategy

### 1. Layered Error Handling
```
Global Exception Middleware (Top Level)
    ↓
Service-Level Error Handling
    ↓
HTTP Client Error Handling
    ↓
External Service Errors
```

### 2. Error Types
- **Validation Errors**: 400 Bad Request
- **Authentication Errors**: 401 Unauthorized
- **Not Found Errors**: 404 Not Found
- **Timeout Errors**: 408 Request Timeout
- **External Service Errors**: 502 Bad Gateway
- **Internal Errors**: 500 Internal Server Error

### 3. Error Response Format
```json
{
  "success": false,
  "error": "Human-readable error message",
  "timestamp": "2024-01-01T12:00:00Z",
  "traceId": "unique-trace-id"
}
```

## Security Considerations

### 1. Input Validation
- All user inputs are validated
- SQL injection prevention (though not using SQL directly)
- XSS prevention in web UI

### 2. API Security
- CORS configuration
- Request size limits
- Rate limiting (can be added)

### 3. Configuration Security
- Sensitive data in environment variables
- No hardcoded secrets
- Secure defaults

## Performance Considerations

### 1. Caching
- Session data cached in memory
- HTTP client connection pooling
- Response caching where appropriate

### 2. Async/Await
- All I/O operations are asynchronous
- Non-blocking request processing
- Proper cancellation token usage

### 3. Resource Management
- Proper disposal of resources
- HTTP client factory pattern
- Memory-efficient data structures

## Monitoring and Observability

### 1. Logging
- Structured logging with categories
- Different log levels for different environments
- Request correlation IDs

### 2. Health Checks
- Service dependency monitoring
- Custom health check implementations
- Health check UI available

### 3. Metrics (Future Enhancement)
- Request duration metrics
- Error rate monitoring
- Tool usage statistics

## Scalability Considerations

### 1. Stateless Design
- Services are stateless (except for session cache)
- Horizontal scaling possible
- Load balancer friendly

### 2. External Dependencies
- Resilient to external service failures
- Timeout and retry policies
- Circuit breaker pattern (can be added)

### 3. Database (Future Enhancement)
- Currently using in-memory storage
- Can be extended to use persistent storage
- Database abstraction already in place

## Testing Strategy

### 1. Unit Tests
- Service layer testing
- Mock external dependencies
- Test business logic in isolation

### 2. Integration Tests
- API endpoint testing
- End-to-end scenarios
- External service integration

### 3. Health Check Tests
- Verify monitoring functionality
- Test failure scenarios
- Validate error responses

## Future Enhancements

### 1. Authentication & Authorization
- User authentication
- Role-based access control
- API key management

### 2. Advanced Monitoring
- Application Performance Monitoring (APM)
- Distributed tracing
- Custom metrics and dashboards

### 3. Enhanced AI Features
- Conversation memory
- Tool learning and optimization
- Multi-modal AI support

### 4. Data Persistence
- Database integration
- Audit logging
- Data backup and recovery
