#!/bin/bash

# Vault MCP AI - Development Runner Script
# This script starts all services for development

set -e  # Exit on any error

echo "🚀 Starting Vault MCP AI Development Environment"
echo "==============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to kill process on port
kill_port() {
    local port=$1
    local pid=$(lsof -ti:$port)
    if [ ! -z "$pid" ]; then
        print_warning "Killing process on port $port (PID: $pid)"
        kill -9 $pid 2>/dev/null || true
        sleep 1
    fi
}

# Function to cleanup on exit
cleanup() {
    print_status "Cleaning up processes..."
    
    # Kill background jobs
    jobs -p | xargs -r kill 2>/dev/null || true
    
    # Kill specific ports if they were started by this script
    if [ "$STARTED_VAULT_API" = true ]; then
        kill_port 4000
    fi
    if [ "$STARTED_SERVER" = true ]; then
        kill_port 5001
    fi
    if [ "$STARTED_CLIENT" = true ]; then
        kill_port 5098
    fi
    
    print_status "Cleanup completed"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Check if .NET is installed
if ! command -v dotnet &> /dev/null; then
    print_error ".NET SDK is not installed or not in PATH"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed or not in PATH"
    print_status "Node.js is required for the mock Vault API"
    exit 1
fi

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"
VAULT_API_DIR="$(dirname "$ROOT_DIR")/vault-mock-api"

print_status "Root directory: $ROOT_DIR"
print_status "Vault API directory: $VAULT_API_DIR"

# Parse command line arguments
SKIP_BUILD=false
SKIP_VAULT_API=false
CONFIGURATION="Development"

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --skip-vault-api)
            SKIP_VAULT_API=true
            shift
            ;;
        -c|--configuration)
            CONFIGURATION="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --skip-build              Skip building the solution"
            echo "  --skip-vault-api          Skip starting the mock Vault API"
            echo "  -c, --configuration       Configuration (Development|Debug|Release) [default: Development]"
            echo "  -h, --help                Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Change to root directory
cd "$ROOT_DIR"

# Build solution if not skipped
if [ "$SKIP_BUILD" = false ]; then
    print_header "Building Solution"
    print_status "Building with configuration: $CONFIGURATION"
    
    dotnet build Vault.MCP.sln --configuration "$CONFIGURATION" --verbosity minimal
    if [ $? -eq 0 ]; then
        print_success "Build completed successfully"
    else
        print_error "Build failed"
        exit 1
    fi
else
    print_warning "Skipping build as requested"
fi

# Check ports and warn about conflicts
STARTED_VAULT_API=false
STARTED_SERVER=false
STARTED_CLIENT=false

print_header "Checking Ports"

if check_port 4000; then
    if [ "$SKIP_VAULT_API" = false ]; then
        print_warning "Port 4000 is already in use. The mock Vault API might already be running."
        read -p "Do you want to kill the existing process and start a new one? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            kill_port 4000
        else
            SKIP_VAULT_API=true
            print_status "Skipping Vault API startup"
        fi
    fi
fi

if check_port 5001; then
    print_warning "Port 5001 is already in use. Killing existing process..."
    kill_port 5001
fi

if check_port 5098; then
    print_warning "Port 5098 is already in use. Killing existing process..."
    kill_port 5098
fi

# Start Mock Vault API
if [ "$SKIP_VAULT_API" = false ]; then
    print_header "Starting Mock Vault API"
    
    if [ ! -d "$VAULT_API_DIR" ]; then
        print_error "Mock Vault API directory not found: $VAULT_API_DIR"
        print_status "Please ensure the vault-mock-api directory exists"
        exit 1
    fi
    
    if [ ! -f "$VAULT_API_DIR/index.js" ]; then
        print_error "Mock Vault API index.js not found in: $VAULT_API_DIR"
        exit 1
    fi
    
    cd "$VAULT_API_DIR"
    print_status "Starting mock Vault API on port 4000..."
    
    # Start in background and capture PID
    node index.js > /tmp/vault-api.log 2>&1 &
    VAULT_API_PID=$!
    STARTED_VAULT_API=true
    
    # Wait a moment and check if it started successfully
    sleep 2
    if kill -0 $VAULT_API_PID 2>/dev/null; then
        print_success "Mock Vault API started successfully (PID: $VAULT_API_PID)"
    else
        print_error "Failed to start mock Vault API"
        print_status "Check log: tail /tmp/vault-api.log"
        exit 1
    fi
    
    cd "$ROOT_DIR"
else
    print_warning "Skipping mock Vault API startup"
fi

# Start MCP Server
print_header "Starting MCP Server"
cd "$ROOT_DIR/server"

print_status "Starting MCP Server on port 5001..."
export ASPNETCORE_ENVIRONMENT="$CONFIGURATION"
export ASPNETCORE_URLS="http://localhost:5001"

dotnet run --no-build > /tmp/mcp-server.log 2>&1 &
SERVER_PID=$!
STARTED_SERVER=true

# Wait a moment and check if it started successfully
sleep 3
if kill -0 $SERVER_PID 2>/dev/null; then
    print_success "MCP Server started successfully (PID: $SERVER_PID)"
else
    print_error "Failed to start MCP Server"
    print_status "Check log: tail /tmp/mcp-server.log"
    cleanup
    exit 1
fi

cd "$ROOT_DIR"

# Start MCP Client
print_header "Starting MCP Client"
cd "$ROOT_DIR/client"

print_status "Starting MCP Client on port 5098..."
export ASPNETCORE_ENVIRONMENT="$CONFIGURATION"
export ASPNETCORE_URLS="http://localhost:5098"

dotnet run --no-build > /tmp/mcp-client.log 2>&1 &
CLIENT_PID=$!
STARTED_CLIENT=true

# Wait a moment and check if it started successfully
sleep 3
if kill -0 $CLIENT_PID 2>/dev/null; then
    print_success "MCP Client started successfully (PID: $CLIENT_PID)"
else
    print_error "Failed to start MCP Client"
    print_status "Check log: tail /tmp/mcp-client.log"
    cleanup
    exit 1
fi

cd "$ROOT_DIR"

# Display status and URLs
print_header "Development Environment Ready! 🎉"
echo ""
print_success "All services are running:"
echo ""
if [ "$SKIP_VAULT_API" = false ]; then
    echo "  📦 Mock Vault API:  http://localhost:4000"
fi
echo "  🔧 MCP Server:      http://localhost:5001"
echo "  🌐 MCP Client:      http://localhost:5098"
echo ""
print_status "Web Interface:     http://localhost:5098"
print_status "Server API Docs:   http://localhost:5001"
print_status "Client API Docs:   http://localhost:5098/swagger"
echo ""
print_status "Health Checks:"
echo "  Server: http://localhost:5001/health"
echo "  Client: http://localhost:5098/health"
echo ""
print_status "Log files:"
if [ "$SKIP_VAULT_API" = false ]; then
    echo "  Vault API: tail -f /tmp/vault-api.log"
fi
echo "  Server:    tail -f /tmp/mcp-server.log"
echo "  Client:    tail -f /tmp/mcp-client.log"
echo ""
print_warning "Press Ctrl+C to stop all services"

# Wait for user interrupt
while true; do
    sleep 1
done
