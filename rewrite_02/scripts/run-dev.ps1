# Vault MCP AI - Development Runner Script (PowerShell)
# This script starts all services for development

param(
    [switch]$SkipBuild,
    [switch]$SkipVaultApi,
    [string]$Configuration = "Development",
    [switch]$Help
)

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
    Magenta = "Magenta"
}

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Colors.Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Colors.Red
}

function Write-Header {
    param([string]$Message)
    Write-Host $Message -ForegroundColor $Colors.Cyan
}

function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

function Stop-ProcessOnPort {
    param([int]$Port)
    try {
        $processes = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue | Select-Object -ExpandProperty OwningProcess
        foreach ($processId in $processes) {
            Write-Warning "Stopping process $processId on port $Port"
            Stop-Process -Id $processId -Force -ErrorAction SilentlyContinue
        }
        Start-Sleep -Seconds 1
    } catch {
        # Ignore errors
    }
}

function Show-Help {
    Write-Host "Vault MCP AI - Development Runner" -ForegroundColor $Colors.Cyan
    Write-Host "Usage: .\run-dev.ps1 [OPTIONS]" -ForegroundColor $Colors.Cyan
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -SkipBuild              Skip building the solution"
    Write-Host "  -SkipVaultApi          Skip starting the mock Vault API"
    Write-Host "  -Configuration <config> Configuration (Development|Debug|Release) [default: Development]"
    Write-Host "  -Help                  Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\run-dev.ps1                    # Start all services"
    Write-Host "  .\run-dev.ps1 -SkipBuild        # Skip build, just run"
    Write-Host "  .\run-dev.ps1 -SkipVaultApi     # Skip mock API"
}

if ($Help) {
    Show-Help
    exit 0
}

Write-Host "🚀 Starting Vault MCP AI Development Environment" -ForegroundColor $Colors.Cyan
Write-Host "===============================================" -ForegroundColor $Colors.Cyan

# Global variables to track started processes
$Global:StartedProcesses = @()
$Global:StartedVaultApi = $false
$Global:StartedServer = $false
$Global:StartedClient = $false

# Cleanup function
function Invoke-Cleanup {
    Write-Status "Cleaning up processes..."
    
    # Stop all tracked processes
    foreach ($process in $Global:StartedProcesses) {
        try {
            if (-not $process.HasExited) {
                Write-Warning "Stopping process $($process.Id)"
                $process.Kill()
            }
        } catch {
            # Ignore errors during cleanup
        }
    }
    
    # Stop processes on specific ports if we started them
    if ($Global:StartedVaultApi) {
        Stop-ProcessOnPort 4000
    }
    if ($Global:StartedServer) {
        Stop-ProcessOnPort 5001
    }
    if ($Global:StartedClient) {
        Stop-ProcessOnPort 5098
    }
    
    Write-Status "Cleanup completed"
}

# Register cleanup on exit
Register-EngineEvent PowerShell.Exiting -Action { Invoke-Cleanup }

# Handle Ctrl+C
[Console]::TreatControlCAsInput = $false
[Console]::CancelKeyPress += {
    param($sender, $e)
    $e.Cancel = $true
    Invoke-Cleanup
    exit 0
}

# Check if .NET is installed
try {
    $dotnetVersion = dotnet --version
    Write-Status "Using .NET SDK version: $dotnetVersion"
} catch {
    Write-Error ".NET SDK is not installed or not in PATH"
    exit 1
}

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Status "Using Node.js version: $nodeVersion"
} catch {
    if (-not $SkipVaultApi) {
        Write-Error "Node.js is not installed or not in PATH"
        Write-Status "Node.js is required for the mock Vault API"
        exit 1
    }
}

# Get script directory and root directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$RootDir = Split-Path -Parent $ScriptDir
$VaultApiDir = Join-Path (Split-Path -Parent $RootDir) "vault-mock-api"

Write-Status "Root directory: $RootDir"
Write-Status "Vault API directory: $VaultApiDir"

# Change to root directory
Set-Location $RootDir

# Build solution if not skipped
if (-not $SkipBuild) {
    Write-Header "Building Solution"
    Write-Status "Building with configuration: $Configuration"
    
    $buildResult = dotnet build Vault.MCP.sln --configuration $Configuration --verbosity minimal
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Build completed successfully"
    } else {
        Write-Error "Build failed"
        exit 1
    }
} else {
    Write-Warning "Skipping build as requested"
}

# Check ports and handle conflicts
Write-Header "Checking Ports"

if (Test-Port 4000) {
    if (-not $SkipVaultApi) {
        Write-Warning "Port 4000 is already in use. The mock Vault API might already be running."
        $response = Read-Host "Do you want to kill the existing process and start a new one? (y/N)"
        if ($response -eq "y" -or $response -eq "Y") {
            Stop-ProcessOnPort 4000
        } else {
            $SkipVaultApi = $true
            Write-Status "Skipping Vault API startup"
        }
    }
}

if (Test-Port 5001) {
    Write-Warning "Port 5001 is already in use. Stopping existing process..."
    Stop-ProcessOnPort 5001
}

if (Test-Port 5098) {
    Write-Warning "Port 5098 is already in use. Stopping existing process..."
    Stop-ProcessOnPort 5098
}

# Start Mock Vault API
if (-not $SkipVaultApi) {
    Write-Header "Starting Mock Vault API"
    
    if (-not (Test-Path $VaultApiDir)) {
        Write-Error "Mock Vault API directory not found: $VaultApiDir"
        Write-Status "Please ensure the vault-mock-api directory exists"
        exit 1
    }
    
    $indexPath = Join-Path $VaultApiDir "index.js"
    if (-not (Test-Path $indexPath)) {
        Write-Error "Mock Vault API index.js not found in: $VaultApiDir"
        exit 1
    }
    
    Write-Status "Starting mock Vault API on port 4000..."
    
    $vaultApiProcess = Start-Process -FilePath "node" -ArgumentList "index.js" -WorkingDirectory $VaultApiDir -PassThru -WindowStyle Hidden
    $Global:StartedProcesses += $vaultApiProcess
    $Global:StartedVaultApi = $true
    
    # Wait and check if it started successfully
    Start-Sleep -Seconds 2
    if (-not $vaultApiProcess.HasExited) {
        Write-Success "Mock Vault API started successfully (PID: $($vaultApiProcess.Id))"
    } else {
        Write-Error "Failed to start mock Vault API"
        exit 1
    }
} else {
    Write-Warning "Skipping mock Vault API startup"
}

# Start MCP Server
Write-Header "Starting MCP Server"
Set-Location (Join-Path $RootDir "server")

Write-Status "Starting MCP Server on port 5001..."
$env:ASPNETCORE_ENVIRONMENT = $Configuration
$env:ASPNETCORE_URLS = "http://localhost:5001"

$serverProcess = Start-Process -FilePath "dotnet" -ArgumentList "run", "--no-build" -PassThru -WindowStyle Hidden
$Global:StartedProcesses += $serverProcess
$Global:StartedServer = $true

# Wait and check if it started successfully
Start-Sleep -Seconds 3
if (-not $serverProcess.HasExited) {
    Write-Success "MCP Server started successfully (PID: $($serverProcess.Id))"
} else {
    Write-Error "Failed to start MCP Server"
    Invoke-Cleanup
    exit 1
}

# Start MCP Client
Write-Header "Starting MCP Client"
Set-Location (Join-Path $RootDir "client")

Write-Status "Starting MCP Client on port 5098..."
$env:ASPNETCORE_ENVIRONMENT = $Configuration
$env:ASPNETCORE_URLS = "http://localhost:5098"

$clientProcess = Start-Process -FilePath "dotnet" -ArgumentList "run", "--no-build" -PassThru -WindowStyle Hidden
$Global:StartedProcesses += $clientProcess
$Global:StartedClient = $true

# Wait and check if it started successfully
Start-Sleep -Seconds 3
if (-not $clientProcess.HasExited) {
    Write-Success "MCP Client started successfully (PID: $($clientProcess.Id))"
} else {
    Write-Error "Failed to start MCP Client"
    Invoke-Cleanup
    exit 1
}

Set-Location $RootDir

# Display status and URLs
Write-Header "Development Environment Ready! 🎉"
Write-Host ""
Write-Success "All services are running:"
Write-Host ""
if (-not $SkipVaultApi) {
    Write-Host "  📦 Mock Vault API:  http://localhost:4000"
}
Write-Host "  🔧 MCP Server:      http://localhost:5001"
Write-Host "  🌐 MCP Client:      http://localhost:5098"
Write-Host ""
Write-Status "Web Interface:     http://localhost:5098"
Write-Status "Server API Docs:   http://localhost:5001"
Write-Status "Client API Docs:   http://localhost:5098/swagger"
Write-Host ""
Write-Status "Health Checks:"
Write-Host "  Server: http://localhost:5001/health"
Write-Host "  Client: http://localhost:5098/health"
Write-Host ""
Write-Warning "Press Ctrl+C to stop all services"

# Wait for user interrupt
try {
    while ($true) {
        Start-Sleep -Seconds 1
        
        # Check if any process has exited unexpectedly
        foreach ($process in $Global:StartedProcesses) {
            if ($process.HasExited) {
                Write-Error "Process $($process.Id) has exited unexpectedly"
                Invoke-Cleanup
                exit 1
            }
        }
    }
} finally {
    Invoke-Cleanup
}
