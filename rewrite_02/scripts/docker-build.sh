#!/bin/bash

# Vault MCP AI - Docker Build Script
# This script builds Docker images for the application

set -e  # Exit on any error

echo "🐳 Building Vault MCP AI Docker Images"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Check if Docker is installed and running
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running"
    exit 1
fi

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"

print_status "Root directory: $ROOT_DIR"

# Change to root directory
cd "$ROOT_DIR"

# Parse command line arguments
TAG_PREFIX="vault-mcp"
TAG_VERSION="latest"
BUILD_SERVER=true
BUILD_CLIENT=true
BUILD_COMPOSE=false
PUSH_IMAGES=false
NO_CACHE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --tag-prefix)
            TAG_PREFIX="$2"
            shift 2
            ;;
        --tag-version)
            TAG_VERSION="$2"
            shift 2
            ;;
        --server-only)
            BUILD_SERVER=true
            BUILD_CLIENT=false
            shift
            ;;
        --client-only)
            BUILD_SERVER=false
            BUILD_CLIENT=true
            shift
            ;;
        --compose)
            BUILD_COMPOSE=true
            shift
            ;;
        --push)
            PUSH_IMAGES=true
            shift
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --tag-prefix <prefix>     Docker image tag prefix [default: vault-mcp]"
            echo "  --tag-version <version>   Docker image tag version [default: latest]"
            echo "  --server-only            Build only the server image"
            echo "  --client-only            Build only the client image"
            echo "  --compose                Build using docker-compose"
            echo "  --push                   Push images to registry after building"
            echo "  --no-cache               Build without using cache"
            echo "  -h, --help               Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_status "Tag prefix: $TAG_PREFIX"
print_status "Tag version: $TAG_VERSION"

# Create Dockerfiles if they don't exist
create_server_dockerfile() {
    cat > server/Dockerfile << 'EOF'
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 5001

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["server/Vault.MCP.Server.csproj", "server/"]
COPY ["shared/Vault.MCP.Shared.csproj", "shared/"]
RUN dotnet restore "server/Vault.MCP.Server.csproj"
COPY . .
WORKDIR "/src/server"
RUN dotnet build "Vault.MCP.Server.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Vault.MCP.Server.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Vault.MCP.Server.dll"]
EOF
    print_status "Created server/Dockerfile"
}

create_client_dockerfile() {
    cat > client/Dockerfile << 'EOF'
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 5098

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["client/Vault.MCP.Client.csproj", "client/"]
COPY ["shared/Vault.MCP.Shared.csproj", "shared/"]
RUN dotnet restore "client/Vault.MCP.Client.csproj"
COPY . .
WORKDIR "/src/client"
RUN dotnet build "Vault.MCP.Client.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Vault.MCP.Client.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Vault.MCP.Client.dll"]
EOF
    print_status "Created client/Dockerfile"
}

create_docker_compose() {
    cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  vault-mcp-server:
    build:
      context: .
      dockerfile: server/Dockerfile
    ports:
      - "5001:5001"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:5001
      - VaultApi__BaseUrl=http://vault-mock-api:4000
    depends_on:
      - vault-mock-api
    networks:
      - vault-mcp-network
    restart: unless-stopped

  vault-mcp-client:
    build:
      context: .
      dockerfile: client/Dockerfile
    ports:
      - "5098:5098"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:5098
      - McpServer__BaseUrl=http://vault-mcp-server:5001
      - AiApi__ApiKey=${AI_API_KEY:-ecs-00}
    depends_on:
      - vault-mcp-server
    networks:
      - vault-mcp-network
    restart: unless-stopped

  vault-mock-api:
    image: node:18-alpine
    working_dir: /app
    volumes:
      - ../vault-mock-api:/app:ro
    command: node index.js
    ports:
      - "4000:4000"
    networks:
      - vault-mcp-network
    restart: unless-stopped

networks:
  vault-mcp-network:
    driver: bridge

volumes:
  vault-mcp-data:
EOF
    print_status "Created docker-compose.yml"
}

# Create .dockerignore if it doesn't exist
create_dockerignore() {
    cat > .dockerignore << 'EOF'
**/.dockerignore
**/.env
**/.git
**/.gitignore
**/.project
**/.settings
**/.toolstarget
**/.vs
**/.vscode
**/*.*proj.user
**/*.dbmdl
**/*.jfm
**/azds.yaml
**/bin
**/charts
**/docker-compose*
**/Dockerfile*
**/node_modules
**/npm-debug.log
**/obj
**/secrets.dev.yaml
**/values.dev.yaml
LICENSE
README.md
**/.idea
**/*.sln.iml
**/logs
**/*.log
EOF
    print_status "Created .dockerignore"
}

# Build with docker-compose
if [ "$BUILD_COMPOSE" = true ]; then
    print_header "Building with Docker Compose"
    
    create_server_dockerfile
    create_client_dockerfile
    create_docker_compose
    create_dockerignore
    
    COMPOSE_ARGS=""
    if [ "$NO_CACHE" = true ]; then
        COMPOSE_ARGS="--no-cache"
    fi
    
    print_status "Building all services with docker-compose..."
    docker-compose build $COMPOSE_ARGS
    
    if [ $? -eq 0 ]; then
        print_success "Docker Compose build completed successfully"
    else
        print_error "Docker Compose build failed"
        exit 1
    fi
    
    # Tag images with custom tags
    if [ "$TAG_VERSION" != "latest" ] || [ "$TAG_PREFIX" != "vault-mcp" ]; then
        print_status "Tagging images..."
        docker tag rewrite_02_vault-mcp-server:latest $TAG_PREFIX-server:$TAG_VERSION
        docker tag rewrite_02_vault-mcp-client:latest $TAG_PREFIX-client:$TAG_VERSION
    fi
    
else
    # Build individual images
    create_dockerignore
    
    # Build server image
    if [ "$BUILD_SERVER" = true ]; then
        print_header "Building Server Image"
        
        create_server_dockerfile
        
        SERVER_TAG="$TAG_PREFIX-server:$TAG_VERSION"
        print_status "Building server image: $SERVER_TAG"
        
        BUILD_ARGS="--tag $SERVER_TAG --file server/Dockerfile ."
        if [ "$NO_CACHE" = true ]; then
            BUILD_ARGS="--no-cache $BUILD_ARGS"
        fi
        
        docker build $BUILD_ARGS
        
        if [ $? -eq 0 ]; then
            print_success "Server image built successfully: $SERVER_TAG"
        else
            print_error "Server image build failed"
            exit 1
        fi
    fi
    
    # Build client image
    if [ "$BUILD_CLIENT" = true ]; then
        print_header "Building Client Image"
        
        create_client_dockerfile
        
        CLIENT_TAG="$TAG_PREFIX-client:$TAG_VERSION"
        print_status "Building client image: $CLIENT_TAG"
        
        BUILD_ARGS="--tag $CLIENT_TAG --file client/Dockerfile ."
        if [ "$NO_CACHE" = true ]; then
            BUILD_ARGS="--no-cache $BUILD_ARGS"
        fi
        
        docker build $BUILD_ARGS
        
        if [ $? -eq 0 ]; then
            print_success "Client image built successfully: $CLIENT_TAG"
        else
            print_error "Client image build failed"
            exit 1
        fi
    fi
fi

# Push images if requested
if [ "$PUSH_IMAGES" = true ]; then
    print_header "Pushing Images to Registry"
    
    if [ "$BUILD_SERVER" = true ]; then
        SERVER_TAG="$TAG_PREFIX-server:$TAG_VERSION"
        print_status "Pushing server image: $SERVER_TAG"
        docker push $SERVER_TAG
        
        if [ $? -eq 0 ]; then
            print_success "Server image pushed successfully"
        else
            print_error "Failed to push server image"
            exit 1
        fi
    fi
    
    if [ "$BUILD_CLIENT" = true ]; then
        CLIENT_TAG="$TAG_PREFIX-client:$TAG_VERSION"
        print_status "Pushing client image: $CLIENT_TAG"
        docker push $CLIENT_TAG
        
        if [ $? -eq 0 ]; then
            print_success "Client image pushed successfully"
        else
            print_error "Failed to push client image"
            exit 1
        fi
    fi
fi

# Display built images
print_header "Built Images"
echo ""
if [ "$BUILD_COMPOSE" = true ]; then
    docker images | grep -E "(rewrite_02_vault-mcp|$TAG_PREFIX)" | head -10
else
    docker images | grep "$TAG_PREFIX" | head -10
fi

print_success "Docker build completed successfully! 🐳"

# Display usage instructions
echo ""
print_header "Usage Instructions"
echo ""
if [ "$BUILD_COMPOSE" = true ]; then
    echo "To run with docker-compose:"
    echo "  docker-compose up -d"
    echo ""
    echo "To stop:"
    echo "  docker-compose down"
else
    echo "To run the images:"
    if [ "$BUILD_SERVER" = true ]; then
        echo "  docker run -p 5001:5001 $TAG_PREFIX-server:$TAG_VERSION"
    fi
    if [ "$BUILD_CLIENT" = true ]; then
        echo "  docker run -p 5098:5098 $TAG_PREFIX-client:$TAG_VERSION"
    fi
fi
echo ""
echo "Access the application at: http://localhost:5098"
