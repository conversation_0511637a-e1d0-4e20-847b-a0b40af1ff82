using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using System.Text;
using System.Net.Http;

[ApiController]
[Route("/")]
public class MCPServerController : ControllerBase
{
    private static readonly HttpClient httpClient = new HttpClient();
    private const string VaultApiUrl = "http://localhost:4000/AutodeskDM/Services/api/vault/v2/vaults";

    private static readonly List<Tool> tools = new List<Tool>
    {
        new Tool {
            name = "getTime",
            description = "Returns the current server time",
            parameters = new { }
        },
        new Tool {
            name = "getVaultFiles",
            description = "Fetches file versions from the Vault Mock API for a given vaultId. Supports advanced filtering, sorting, and options.",
            parameters = new {
                vaultId = new { type = "string", required = true, description = "Vault ID to query (e.g., 'default' or '117')" },
                q = new { type = "string", required = false, description = "Free text search query" },
                filter_CheckoutUserName = new { type = "string", required = false, description = "Filter by checkout user name" },
                filter_CreateUserName = new { type = "string", required = false, description = "Filter by create user name" },
                filter_CategoryName = new { type = "string", required = false, description = "Filter by category name" },
                filter_State = new { type = "string", required = false, description = "Filter by file state" },
                option_latestOnly = new { type = "boolean", required = false, description = "Only latest version (default: true)" },
                option_releasedFilesOnly = new { type = "boolean", required = false, description = "Only released files (default: false). Use this instead of any status or filter object for released files." },
                option_extendedModels = new { type = "boolean", required = false, description = "Return extended model info (default: false)" },
                option_propDefIds = new { type = "string", required = false, description = "Comma-separated property IDs to return, or 'all'" },
                sort = new { type = "string", required = false, description = "Sort criteria, e.g. 'Revision desc,Name asc'" },
                limit = new { type = "number", required = false, description = "Number of results to return (default: 2)" },
                cursorState = new { type = "string", required = false, description = "Cursor for pagination (not implemented in mock)" }
            }
        }
    };

    [HttpGet("tools")]
    public IActionResult GetTools()
    {
        return Ok(tools);
    }

    [HttpPost("tool")]
    public async Task<IActionResult> UseTool([FromBody] ToolRequest req)
    {
        if (req.tool == "getTime")
        {
            return Ok(new { time = DateTime.UtcNow.ToString("o") });
        }
        else if (req.tool == "getVaultFiles")
        {
            var vaultId = req.@params?.vaultId?.ToString() ?? "117";
            var url = new StringBuilder($"{VaultApiUrl}/{vaultId}/file-versions");
            var query = new List<string>();
            foreach (var <NAME_EMAIL>().GetProperties())
            {
                var key = prop.Name.Replace("_", "[").Replace("option", "option[").Replace("filter", "filter[") + (prop.Name.Contains("option") || prop.Name.Contains("filter") ? "]" : "");
                if (prop.Name == "vaultId") continue;
                var value = prop.GetValue(req.@params)?.ToString();
                if (!string.IsNullOrEmpty(value)) query.Add($"{key}={Uri.EscapeDataString(value)}");
            }
            if (query.Count > 0) url.Append("?" + string.Join("&", query));
            var res = await httpClient.GetAsync(url.ToString());
            var content = await res.Content.ReadAsStringAsync();
            return Content(content, "application/json");
        }
        return BadRequest("Unknown tool");
    }

    public class ToolRequest
    {
        public string tool { get; set; }
        public dynamic @params { get; set; }
    }
    public class Tool
    {
        public string name { get; set; }
        public string description { get; set; }
        public object parameters { get; set; }
    }
}
